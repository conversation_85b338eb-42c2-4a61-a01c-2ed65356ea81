# zackathon - Season 1 🚀

A comprehensive hackathon platform for Web3 builders to create applications using zPass wallet intelligence APIs. Built with Next.js, TypeScript, Tailwind CSS, and Supabase.

![zackathon Banner](https://via.placeholder.com/1200x400/0A0D1F/6366F1?text=zackathon+Season+1)

## 🌟 Features

### 🏠 Landing Page
- **Hero Section** with animated statistics and call-to-action
- **Live Leaderboard** showing top builders and their applications
- **How to Participate** step-by-step guide
- **About zPass** comprehensive information about the APIs
- **FAQ Section** with expandable questions and answers
- **Responsive Design** optimized for all devices

### 📊 Dashboard
- **User Authentication** with Google and email login via Supabase
- **Dashboard Statistics** showing points, rank, API calls, and progress
- **Application Submission** form with validation and file uploads
- **API Key Management** with secure key generation and usage tracking
- **Social Sharing** tools to promote applications on Twitter/X
- **Referral System** to invite other builders and earn bonus points

### 🎯 Points System
- **API Usage Tracking** with different multipliers for API types
- **User Engagement Metrics** based on application usage
- **Bonus Points** for innovation, community engagement, and referrals
- **Real-time Updates** with live leaderboard rankings
- **Anti-gaming Measures** to ensure fair competition

## 🛠️ Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Animations**: Framer Motion for smooth interactions
- **Authentication**: Supabase Auth with Google OAuth
- **Database**: Supabase PostgreSQL with Row Level Security
- **Icons**: Lucide React for consistent iconography
- **Deployment**: Vercel (recommended)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm/yarn/pnpm
- Supabase account and project
- Google OAuth credentials (optional)

### Installation

1. **Clone the repository**
```bash
git clone https://github.com/your-username/zackathon-app.git
cd zackathon-app
```

2. **Install dependencies**
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. **Set up environment variables**
```bash
cp .env.example .env.local
```

Edit `.env.local` with your Supabase credentials:
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

4. **Set up the database**
```bash
# Run the SQL schema in your Supabase SQL editor
cat database/schema.sql
```

5. **Start the development server**
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to see the application.

## 📁 Project Structure

```
zackathon-app/
├── src/
│   ├── app/                    # Next.js App Router pages
│   │   ├── dashboard/          # Dashboard page
│   │   ├── globals.css         # Global styles
│   │   ├── layout.tsx          # Root layout
│   │   └── page.tsx            # Home page
│   ├── components/             # React components
│   │   ├── dashboard/          # Dashboard-specific components
│   │   ├── FAQ.tsx             # FAQ section
│   │   ├── Footer.tsx          # Site footer
│   │   ├── HeroSection.tsx     # Landing hero
│   │   ├── HowToParticipate.tsx # Participation guide
│   │   ├── Leaderboard.tsx     # Leaderboard display
│   │   ├── LoginModal.tsx      # Authentication modal
│   │   └── Navigation.tsx      # Site navigation
│   ├── contexts/               # React contexts
│   │   └── AuthContext.tsx     # Authentication context
│   ├── lib/                    # Utility libraries
│   │   ├── supabase.ts         # Supabase client
│   │   ├── types.ts            # TypeScript types
│   │   └── utils.ts            # Helper functions
├── database/                   # Database schema and migrations
│   └── schema.sql              # Supabase database schema
├── docs/                       # Documentation
│   └── POINTS_SYSTEM.md        # Points calculation guide
├── public/                     # Static assets
├── tailwind.config.ts          # Tailwind configuration
└── package.json                # Dependencies and scripts
```

## 🎨 Design System

The application uses a custom design system based on the zPass brand guidelines:

### Colors
- **Primary**: `#6366F1` (Indigo)
- **Secondary**: `#10B981` (Emerald)
- **Accent**: `#8B5CF6` (Purple)
- **Background**: `#0A0D1F` (Dark Blue)
- **Surface**: `#1A1E35` (Lighter Dark)

### Typography
- **Font Family**: Inter (Google Fonts)
- **Weights**: 300, 400, 500, 600, 700

### Components
- **Cards**: Glass morphism with backdrop blur
- **Buttons**: Gradient backgrounds with hover effects
- **Forms**: Consistent styling with validation states
- **Animations**: Smooth transitions using Framer Motion

## 🔧 Configuration

### Supabase Setup

1. **Create a new Supabase project**
2. **Run the database schema** from `database/schema.sql`
3. **Configure Row Level Security** (RLS) policies
4. **Set up Google OAuth** (optional):
   - Go to Authentication > Settings > Auth Providers
   - Enable Google provider
   - Add your Google OAuth credentials

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Your Supabase project URL | ✅ |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase anonymous key | ✅ |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service role key | ✅ |
| `NEXT_PUBLIC_APP_URL` | Your app's URL | ✅ |
| `ZPASS_API_BASE_URL` | zPass API base URL | ⚠️ |
| `TWITTER_API_KEY` | Twitter API key for sharing | ⚠️ |

## 📊 Database Schema

The application uses the following main tables:

- **users**: User profiles and authentication data
- **applications**: Submitted hackathon applications
- **api_usage**: API call tracking and analytics
- **points_history**: Points calculation and history
- **referrals**: Referral system tracking
- **faqs**: Frequently asked questions

See `database/schema.sql` for the complete schema with indexes and RLS policies.

## 🎯 Points System

The hackathon uses a sophisticated points system to rank participants:

### Base Calculation
```
Points = (API Calls × 10 × API Type Multiplier) + Engagement Bonus + Referral Bonus
```

### API Type Multipliers
- **Wallet Analytics**: 2.0x
- **DeFi Score**: 1.8x
- **Reputation**: 1.5x
- **Transaction History**: 1.2x
- **Basic**: 1.0x

See `docs/POINTS_SYSTEM.md` for detailed information.

## 🚀 Deployment

### Vercel (Recommended)

1. **Connect your repository** to Vercel
2. **Set environment variables** in Vercel dashboard
3. **Deploy** - Vercel will automatically build and deploy

### Manual Deployment

1. **Build the application**
```bash
npm run build
```

2. **Start the production server**
```bash
npm start
```

### Docker Deployment

```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]
```

## 🧪 Testing

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
```

## 📈 Performance

The application is optimized for performance:

- **Next.js App Router** for optimal loading
- **Image Optimization** with Next.js Image component
- **Code Splitting** for smaller bundle sizes
- **Caching** with SWR for API calls
- **Lazy Loading** for components and images

## 🔒 Security

- **Row Level Security** (RLS) in Supabase
- **Input Validation** on all forms
- **API Rate Limiting** to prevent abuse
- **CSRF Protection** built into Next.js
- **Environment Variables** for sensitive data

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch** (`git checkout -b feature/amazing-feature`)
3. **Commit your changes** (`git commit -m 'Add amazing feature'`)
4. **Push to the branch** (`git push origin feature/amazing-feature`)
5. **Open a Pull Request**

### Development Guidelines

- Use TypeScript for all new code
- Follow the existing code style
- Add tests for new features
- Update documentation as needed
- Use conventional commit messages

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **zPass Team** for providing the wallet intelligence APIs
- **Supabase** for the backend infrastructure
- **Vercel** for hosting and deployment
- **Next.js Team** for the amazing framework
- **Tailwind CSS** for the utility-first CSS framework

## 📞 Support

- **Documentation**: [docs.zpass.dev](https://docs.zpass.dev)
- **Discord**: [discord.gg/zpass](https://discord.gg/zpass)
- **Email**: <EMAIL>
- **Twitter**: [@zpass_dev](https://twitter.com/zpass_dev)

---

**Built with ❤️ for the Web3 community**
