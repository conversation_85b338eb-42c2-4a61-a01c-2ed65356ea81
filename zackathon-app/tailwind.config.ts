import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        // Main Background Colors
        background: "#0A0D1F",
        surface: "#1A1E35",
        "surface-light": "#252A47",
        "surface-hover": "#2D3454",
        
        // Primary Brand Colors
        primary: "#6366F1",
        "primary-light": "#818CF8",
        "primary-dark": "#4F46E5",
        secondary: "#10B981",
        "secondary-light": "#34D399",
        
        // Accent Colors
        accent: "#8B5CF6",
        "accent-light": "#A78BFA",
        "accent-cyan": "#06B6D4",
        "accent-pink": "#EC4899",
        "accent-orange": "#F59E0B",
        
        // Status Colors
        success: "#10B981",
        warning: "#F59E0B",
        danger: "#EF4444",
        info: "#3B82F6",
        
        // Text Colors
        "text-primary": "#F8FAFC",
        "text-secondary": "#CBD5E1",
        "text-muted": "#94A3B8",
        "text-accent": "#A78BFA",
        
        // Border & UI Colors
        "border-color": "#374151",
        "border-light": "#4B5563",
        "border-accent": "#6366F1",
        
        // Chart Colors
        "chart-1": "#6366F1",
        "chart-2": "#10B981",
        "chart-3": "#F59E0B",
        "chart-4": "#EF4444",
        "chart-5": "#8B5CF6",
        "chart-6": "#06B6D4",
        "chart-7": "#EC4899",
        "chart-8": "#84CC16",
      },
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      fontSize: {
        'xs': '12px',
        'sm': '14px',
        'base': '16px',
        'lg': '18px',
        'xl': '20px',
        '2xl': '24px',
        '3xl': '30px',
        '4xl': '36px',
        '5xl': '48px',
      },
      fontWeight: {
        light: '300',
        normal: '400',
        medium: '500',
        semibold: '600',
        bold: '700',
      },
      backgroundImage: {
        'gradient-primary': 'linear-gradient(135deg, #6366F1, #8B5CF6)',
        'gradient-body': 'linear-gradient(135deg, #0A0D1F 0%, #0F1629 50%, #1A1E35 100%)',
        'gradient-card': 'linear-gradient(135deg, #1A1E35, #252A47)',
      },
      boxShadow: {
        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)',
        'card-hover': '0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3)',
        'glow-blue': '0 0 20px rgba(59, 130, 246, 0.3)',
        'glow-purple': '0 0 20px rgba(139, 92, 246, 0.3)',
        'glow-emerald': '0 0 20px rgba(16, 185, 129, 0.3)',
      },
      backdropBlur: {
        'card': '20px',
      },
      screens: {
        'xs': '475px',
        'sm': '640px',
        'md': '768px',
        'lg': '1024px',
        'xl': '1280px',
        '2xl': '1536px',
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.5s ease-out',
        'glow': 'glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(20px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        glow: {
          '0%': { boxShadow: '0 0 20px rgba(139, 92, 246, 0.3)' },
          '100%': { boxShadow: '0 0 30px rgba(139, 92, 246, 0.6)' },
        },
      },
    },
  },
  plugins: [],
};

export default config;
