# zackathon Season 1 - Points Calculation System

## Overview

The zackathon points system is designed to reward builders based on their API usage, innovation, and community engagement. Points are calculated using multiple factors to ensure fair competition and encourage meaningful development.

## Base Point Calculation

### Formula
```
Total Points = (API Calls × Base Multiplier × API Type Multiplier) + Engagement Bonus + Referral Bonus + Innovation Bonus
```

### Base Metrics

#### 1. API Volume Points
- **Base Rate**: 10 points per API call
- **Minimum Threshold**: 100 API calls to qualify for leaderboard
- **Maximum Daily Limit**: 1,000 API calls counted per day (prevents spam)

#### 2. API Type Multipliers
Different zPass APIs have different complexity and value multipliers:

| API Type | Multiplier | Description |
|----------|------------|-------------|
| Wallet Analytics | 2.0x | Advanced wallet behavior analysis |
| DeFi Score | 1.8x | Comprehensive DeFi activity scoring |
| Reputation System | 1.5x | On-chain reputation calculation |
| Transaction History | 1.2x | Historical transaction analysis |
| Basic Wallet Info | 1.0x | Standard wallet information |

#### 3. User Engagement Metrics
- **Active Users**: 5 points per unique user interaction with your app
- **Daily Active Users**: 10 points per daily active user
- **User Retention**: 20 points per user who returns after 7 days
- **Session Duration**: 1 point per minute of average session time

## Bonus Point Categories

### 1. Innovation Bonus (Manual Review)
- **Novel Use Case**: 500-2000 points
- **Technical Excellence**: 300-1000 points
- **User Experience**: 200-800 points
- **Integration Quality**: 100-500 points

### 2. Community Engagement Bonus
- **GitHub Repository**: 200 points (one-time)
- **Open Source License**: 300 points (one-time)
- **Documentation Quality**: 100-400 points
- **Social Media Sharing**: 50 points per share (max 500/week)
- **Community Feedback**: 10 points per positive review

### 3. Referral System
- **Successful Referral**: 100 points (both referrer and referee)
- **Referee Requirement**: Must submit at least one working application
- **Maximum Referrals**: 50 per participant

### 4. Milestone Bonuses
- **First Application**: 100 points
- **Multiple Applications**: 200 points for 2nd app, 300 for 3rd, etc.
- **Cross-API Usage**: 250 points for using 3+ different API types
- **High Volume**: 500 points for 10K+ API calls
- **Consistency**: 300 points for 7+ days of consecutive API usage

## Quality Assurance Measures

### Anti-Gaming Mechanisms
1. **Rate Limiting**: Maximum 1,000 API calls per day counted
2. **Unique User Validation**: User engagement must come from different IP addresses
3. **Manual Review**: Top 20 applications undergo manual review
4. **Suspicious Activity Detection**: Automated flagging of unusual patterns

### Application Requirements
- **Functional Application**: Must be publicly accessible and working
- **Real Usage**: Must demonstrate actual user interaction
- **API Integration**: Must properly implement zPass APIs
- **Code Quality**: Clean, documented code (for bonus points)

## Leaderboard Updates

### Update Frequency
- **Real-time**: API call counts updated every 15 minutes
- **Daily**: User engagement metrics updated daily at midnight UTC
- **Weekly**: Manual review bonuses applied every Sunday

### Ranking Algorithm
1. Total points (primary)
2. API call volume (tiebreaker)
3. Number of unique users (secondary tiebreaker)
4. Application submission date (final tiebreaker)

## Example Calculations

### Example 1: DeFi Portfolio Tracker
- **API Calls**: 5,000 (wallet-analytics)
- **Base Points**: 5,000 × 10 = 50,000
- **API Multiplier**: 50,000 × 2.0 = 100,000
- **User Engagement**: 200 users × 5 = 1,000
- **GitHub Bonus**: 200
- **Innovation Bonus**: 1,500 (novel use case)
- **Total**: 102,700 points

### Example 2: Simple Wallet Viewer
- **API Calls**: 1,200 (basic wallet info)
- **Base Points**: 1,200 × 10 = 12,000
- **API Multiplier**: 12,000 × 1.0 = 12,000
- **User Engagement**: 50 users × 5 = 250
- **First App Bonus**: 100
- **Total**: 12,350 points

## Prize Distribution

### Prize Categories
1. **Grand Prize** (Highest Points): $15,000
2. **Innovation Award** (Manual Review): $10,000
3. **Best UX** (User Engagement): $8,000
4. **Community Choice** (Social Voting): $5,000
5. **Top 10 Finishers**: $1,000 each
6. **Participation Prizes**: $200 for all qualifying submissions

### Qualification Requirements
- Minimum 1,000 total points
- Functional application with public URL
- Proper zPass API integration
- No violations of terms of service

## Appeals Process

### Dispute Resolution
1. **Initial Review**: Automated system flags discrepancies
2. **Manual Review**: Human review within 48 hours
3. **Appeal Process**: 7-day window for appeals
4. **Final Decision**: Review board decision within 3 business days

### Common Issues
- **Missing Points**: Usually due to API key misconfiguration
- **Bonus Disputes**: Manual review bonuses are subjective
- **Technical Issues**: System downtime adjustments made case-by-case

## Technical Implementation

### Database Schema
```sql
-- Points tracking table
CREATE TABLE points_history (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  application_id UUID REFERENCES applications(id),
  points_earned INTEGER,
  point_type VARCHAR(50), -- 'api_usage', 'engagement', 'bonus', 'referral'
  api_calls INTEGER DEFAULT 0,
  api_type VARCHAR(50),
  metadata JSONB, -- Additional context
  created_at TIMESTAMP DEFAULT NOW()
);
```

### API Endpoints
- `GET /api/points/:userId` - Get user's total points
- `GET /api/leaderboard` - Get current leaderboard
- `POST /api/points/calculate` - Recalculate points (admin)
- `GET /api/points/history/:userId` - Get points history

## Updates and Changes

This points system may be updated during the hackathon to ensure fairness and address any gaming attempts. All participants will be notified of any changes with at least 48 hours notice.

**Last Updated**: January 2025
**Version**: 1.0
