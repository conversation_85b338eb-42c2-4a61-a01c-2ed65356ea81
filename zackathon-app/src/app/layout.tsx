import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/AuthContext";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "zackathon - Season 1 | Build on zPass",
  description: "Join zackathon Season 1 - A hackathon for Web3 builders to create applications using zPass wallet intelligence APIs. Build, compete, and win!",
  keywords: "hackathon, web3, zPass, wallet intelligence, DeFi, blockchain, API",
  authors: [{ name: "zPass Team" }],
  openGraph: {
    title: "zackathon - Season 1 | Build on zPass",
    description: "Join zackathon Season 1 - A hackathon for Web3 builders to create applications using zPass wallet intelligence APIs.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "zackathon - Season 1 | Build on zPass",
    description: "Jo<PERSON> zackathon Season 1 - A hackathon for Web3 builders to create applications using zPass wallet intelligence APIs.",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body className={`${inter.variable} font-sans antialiased`}>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
