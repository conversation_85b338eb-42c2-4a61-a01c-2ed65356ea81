import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}

export function formatDate(date: Date): string {
  return new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }).format(date)
}

export function truncateAddress(address: string, chars = 4): string {
  return `${address.slice(0, chars + 2)}...${address.slice(-chars)}`
}

export function calculatePoints(apiCalls: number, apiType: string, userEngagement: number): number {
  const basePoints = apiCalls * 10
  
  // API type multipliers
  const typeMultipliers: Record<string, number> = {
    'wallet-analytics': 2.0,
    'defi-score': 1.8,
    'reputation': 1.5,
    'transaction-history': 1.2,
    'basic': 1.0,
  }
  
  const typeMultiplier = typeMultipliers[apiType] || 1.0
  const engagementBonus = userEngagement * 5
  
  return Math.floor(basePoints * typeMultiplier + engagementBonus)
}

export function getRankSuffix(rank: number): string {
  if (rank % 100 >= 11 && rank % 100 <= 13) {
    return 'th'
  }
  switch (rank % 10) {
    case 1: return 'st'
    case 2: return 'nd'
    case 3: return 'rd'
    default: return 'th'
  }
}

export function validateApiKey(apiKey: string): boolean {
  // Basic API key validation - should be 32+ characters alphanumeric
  return /^[a-zA-Z0-9]{32,}$/.test(apiKey)
}

export function generateShareText(appName: string, rank: number, points: number): string {
  return `🚀 Just built ${appName} using @zPass APIs and ranked #${rank} in #zackathon Season 1! 
  
💎 ${points} points earned building on wallet-level intelligence
🔥 Join the hackathon: zackathon.dev
  
#Web3 #BuildOnzPass #Hackathon`
}
