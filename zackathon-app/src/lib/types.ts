export interface User {
  id: string
  email: string
  name?: string
  avatar_url?: string
  created_at: string
  updated_at: string
}

export interface Application {
  id: string
  user_id: string
  name: string
  description: string
  app_url: string
  github_url?: string
  api_key: string
  api_calls_count: number
  api_types_used: string[]
  user_engagement_score: number
  points: number
  rank?: number
  is_active: boolean
  created_at: string
  updated_at: string
  user?: User
}

export interface ApiUsage {
  id: string
  application_id: string
  api_key: string
  endpoint: string
  api_type: string
  calls_count: number
  date: string
  created_at: string
}

export interface Referral {
  id: string
  referrer_id: string
  referred_id: string
  bonus_points: number
  created_at: string
  referrer?: User
  referred?: User
}

export interface LeaderboardEntry {
  rank: number
  user_name: string
  app_name: string
  app_url: string
  points: number
  api_calls: number
  user_id: string
  application_id: string
}

export interface PointsHistory {
  id: string
  user_id: string
  application_id: string
  points_earned: number
  reason: string
  api_calls: number
  api_type: string
  created_at: string
}

export interface FAQ {
  id: string
  question: string
  answer: string
  category: string
  order: number
}

export interface ApiEndpoint {
  name: string
  description: string
  category: string
  points_multiplier: number
  documentation_url: string
}

export const API_CATEGORIES = {
  WALLET_ANALYTICS: 'wallet-analytics',
  DEFI_SCORE: 'defi-score',
  REPUTATION: 'reputation',
  TRANSACTION_HISTORY: 'transaction-history',
  BASIC: 'basic',
} as const

export type ApiCategory = typeof API_CATEGORIES[keyof typeof API_CATEGORIES]

export interface DashboardStats {
  totalPoints: number
  totalApiCalls: number
  currentRank: number
  applicationsCount: number
  referralsCount: number
  pointsThisWeek: number
  apiCallsThisWeek: number
}
