'use client'

import { motion } from 'framer-motion'
import { UserPlus, Key, Code, Upload, Trophy, ArrowRight } from 'lucide-react'
import Link from 'next/link'

const steps = [
  {
    icon: UserPlus,
    title: 'Sign Up',
    description: 'Create your account using email or Google authentication',
    details: 'Quick and secure registration to get started with zackathon',
  },
  {
    icon: Key,
    title: 'Get API Key',
    description: 'Access your zPass API key from the dashboard',
    details: 'Free tier includes 10,000 API calls to get you started',
  },
  {
    icon: Code,
    title: 'Build Your App',
    description: 'Create innovative applications using zPass APIs',
    details: 'Use wallet analytics, DeFi scores, and reputation data',
  },
  {
    icon: Upload,
    title: 'Submit Project',
    description: 'Upload your application and provide the live URL',
    details: 'Include GitHub repository for bonus points',
  },
  {
    icon: Trophy,
    title: 'Compete & Win',
    description: 'Climb the leaderboard and win amazing prizes',
    details: 'Points based on API usage, innovation, and user engagement',
  },
]

export default function HowToParticipate() {
  return (
    <section id="participate" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-text-primary mb-4">
            How to Participate
          </h2>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            Join zackathon Season 1 in just 5 simple steps and start building the future of Web3
          </p>
        </motion.div>

        <div className="relative">
          {/* Connection Lines */}
          <div className="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-primary via-accent to-secondary transform -translate-y-1/2 opacity-30"></div>

          {/* Steps */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-4">
            {steps.map((step, index) => (
              <motion.div
                key={step.title}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="relative"
              >
                {/* Step Card */}
                <div className="card p-6 text-center hover:glow-purple relative z-10">
                  {/* Step Number */}
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center text-white font-bold text-sm">
                    {index + 1}
                  </div>

                  {/* Icon */}
                  <div className="mb-4 flex justify-center">
                    <div className="w-16 h-16 bg-surface-light rounded-full flex items-center justify-center">
                      <step.icon className="w-8 h-8 text-accent" />
                    </div>
                  </div>

                  {/* Content */}
                  <h3 className="text-xl font-semibold text-text-primary mb-2">
                    {step.title}
                  </h3>
                  <p className="text-text-secondary mb-3">
                    {step.description}
                  </p>
                  <p className="text-sm text-text-muted">
                    {step.details}
                  </p>
                </div>

                {/* Arrow for desktop */}
                {index < steps.length - 1 && (
                  <div className="hidden lg:block absolute top-1/2 -right-2 transform -translate-y-1/2 z-20">
                    <ArrowRight className="w-6 h-6 text-accent" />
                  </div>
                )}
              </motion.div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          viewport={{ once: true }}
          className="text-center mt-16"
        >
          <div className="card p-8 bg-gradient-card">
            <h3 className="text-2xl font-bold text-text-primary mb-4">
              Ready to Start Building?
            </h3>
            <p className="text-text-secondary mb-6 max-w-2xl mx-auto">
              Join hundreds of developers already building innovative Web3 applications with zPass APIs. 
              Get started today and compete for amazing prizes!
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <Link
                href="/dashboard"
                className="btn-primary flex items-center space-x-2"
              >
                <span>Get Started Now</span>
                <ArrowRight className="w-5 h-5" />
              </Link>
              <Link
                href="#about"
                className="btn-secondary"
              >
                Learn About zPass
              </Link>
            </div>
          </div>
        </motion.div>

        {/* Quick Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
          viewport={{ once: true }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12"
        >
          <div className="text-center">
            <div className="text-3xl font-bold text-primary mb-2">$50K</div>
            <div className="text-text-muted">Total Prize Pool</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-secondary mb-2">8 Weeks</div>
            <div className="text-text-muted">Competition Duration</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold text-accent mb-2">10K+</div>
            <div className="text-text-muted">Free API Calls</div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
