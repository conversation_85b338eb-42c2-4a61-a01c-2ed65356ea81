'use client'

import { motion } from 'framer-motion'
import { Brain, Shield, Zap, TrendingUp, Users, Code, ExternalLink } from 'lucide-react'

const features = [
  {
    icon: Brain,
    title: 'Wallet Intelligence',
    description: 'Advanced analytics and insights into wallet behavior, transaction patterns, and DeFi activity.',
  },
  {
    icon: Shield,
    title: 'Reputation Scoring',
    description: 'Comprehensive reputation system based on on-chain activity, transaction history, and protocol interactions.',
  },
  {
    icon: TrendingUp,
    title: 'DeFi Analytics',
    description: 'Deep insights into DeFi positions, yield farming strategies, and protocol usage patterns.',
  },
  {
    icon: Zap,
    title: 'Real-time Data',
    description: 'Live data feeds with sub-second latency for the most up-to-date wallet intelligence.',
  },
]

const useCases = [
  {
    title: 'DeFi Protocols',
    description: 'Risk assessment, user scoring, and personalized recommendations',
    examples: ['Lending risk evaluation', 'Yield optimization', 'Protocol governance'],
  },
  {
    title: 'NFT Marketplaces',
    description: 'User verification, collection analytics, and trading insights',
    examples: ['Collector profiles', 'Market trends', 'Authenticity verification'],
  },
  {
    title: 'Web3 Social',
    description: 'Identity verification, reputation systems, and social scoring',
    examples: ['Social credit', 'Community building', 'Influence metrics'],
  },
  {
    title: 'Portfolio Trackers',
    description: 'Comprehensive portfolio analytics and performance tracking',
    examples: ['Asset allocation', 'Performance metrics', 'Risk analysis'],
  },
]

export default function AboutzPass() {
  return (
    <section id="about" className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-text-primary mb-4">
            About zPass
          </h2>
          <p className="text-lg text-text-secondary max-w-3xl mx-auto">
            zPass is the leading wallet-level intelligence API that provides developers with comprehensive 
            insights into Web3 user behavior, DeFi activity, and on-chain reputation.
          </p>
        </motion.div>

        {/* Features Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="card p-6 text-center hover:glow-purple"
            >
              <div className="w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <feature.icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-text-primary mb-3">
                {feature.title}
              </h3>
              <p className="text-text-secondary">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </div>

        {/* Use Cases */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="mb-20"
        >
          <div className="text-center mb-12">
            <h3 className="text-2xl sm:text-3xl font-bold text-text-primary mb-4">
              Use Cases & Applications
            </h3>
            <p className="text-lg text-text-secondary max-w-2xl mx-auto">
              Build innovative applications across various Web3 verticals using zPass APIs
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {useCases.map((useCase, index) => (
              <motion.div
                key={useCase.title}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="card p-6"
              >
                <h4 className="text-xl font-semibold text-text-primary mb-3">
                  {useCase.title}
                </h4>
                <p className="text-text-secondary mb-4">
                  {useCase.description}
                </p>
                <div className="space-y-2">
                  {useCase.examples.map((example, idx) => (
                    <div key={idx} className="flex items-center space-x-2">
                      <div className="w-1.5 h-1.5 bg-accent rounded-full"></div>
                      <span className="text-text-muted text-sm">{example}</span>
                    </div>
                  ))}
                </div>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* API Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="card p-8 bg-gradient-card mb-12"
        >
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-text-primary mb-4">
              Trusted by Developers Worldwide
            </h3>
            <p className="text-text-secondary">
              Join thousands of developers building the future of Web3 with zPass
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-3xl font-bold text-primary mb-2">500M+</div>
              <div className="text-text-muted">API Calls Served</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-secondary mb-2">10K+</div>
              <div className="text-text-muted">Developers</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-accent mb-2">50+</div>
              <div className="text-text-muted">Protocols Integrated</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-warning mb-2">99.9%</div>
              <div className="text-text-muted">Uptime</div>
            </div>
          </div>
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center"
        >
          <div className="card p-8">
            <h3 className="text-2xl font-bold text-text-primary mb-4">
              Ready to Build with zPass?
            </h3>
            <p className="text-text-secondary mb-6 max-w-2xl mx-auto">
              Get started with our comprehensive APIs and join the zackathon to compete for amazing prizes
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <a
                href="https://docs.zpass.dev"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary flex items-center space-x-2"
              >
                <span>View Documentation</span>
                <ExternalLink className="w-4 h-4" />
              </a>
              <a
                href="/dashboard"
                className="btn-secondary"
              >
                Start Building
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
