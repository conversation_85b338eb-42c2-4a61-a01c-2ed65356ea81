'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { <PERSON>, <PERSON><PERSON>, Eye, EyeOff, RefreshCw, ExternalLink } from 'lucide-react'

export default function ApiKeySection() {
  const [showApiKey, setShowApiKey] = useState(false)
  const [copied, setCopied] = useState(false)
  
  // Mock API key - replace with real data
  const apiKey = 'zp_live_1234567890abcdef1234567890abcdef'
  const maskedApiKey = 'zp_live_••••••••••••••••••••••••••••••••'

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(apiKey)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const regenerateApiKey = () => {
    // TODO: Implement API key regeneration
    console.log('Regenerating API key...')
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      className="card p-6"
    >
      <div className="flex items-center space-x-3 mb-4">
        <div className="p-2 rounded-lg bg-primary/10">
          <Key className="w-5 h-5 text-primary" />
        </div>
        <h3 className="text-lg font-semibold text-text-primary">
          API Key
        </h3>
      </div>

      <p className="text-text-secondary text-sm mb-4">
        Use this API key to authenticate your requests to zPass APIs. Keep it secure and don't share it publicly.
      </p>

      {/* API Key Display */}
      <div className="space-y-4">
        <div className="relative">
          <div className="flex items-center space-x-2 p-3 bg-surface-light border border-border-color rounded-lg">
            <code className="flex-1 text-sm font-mono text-text-primary">
              {showApiKey ? apiKey : maskedApiKey}
            </code>
            <div className="flex items-center space-x-1">
              <button
                onClick={() => setShowApiKey(!showApiKey)}
                className="p-1 rounded text-text-muted hover:text-text-primary transition-colors duration-200"
                title={showApiKey ? 'Hide API key' : 'Show API key'}
              >
                {showApiKey ? <EyeOff className="w-4 h-4" /> : <Eye className="w-4 h-4" />}
              </button>
              <button
                onClick={copyToClipboard}
                className="p-1 rounded text-text-muted hover:text-text-primary transition-colors duration-200"
                title="Copy API key"
              >
                <Copy className="w-4 h-4" />
              </button>
            </div>
          </div>
          {copied && (
            <div className="absolute -top-8 right-0 bg-success text-white text-xs px-2 py-1 rounded">
              Copied!
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="flex flex-col space-y-2">
          <button
            onClick={regenerateApiKey}
            className="btn-secondary flex items-center justify-center space-x-2 text-sm"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Regenerate Key</span>
          </button>
          <a
            href="https://docs.zpass.dev/authentication"
            target="_blank"
            rel="noopener noreferrer"
            className="btn-secondary flex items-center justify-center space-x-2 text-sm"
          >
            <span>View Documentation</span>
            <ExternalLink className="w-4 h-4" />
          </a>
        </div>
      </div>

      {/* Usage Stats */}
      <div className="mt-6 pt-6 border-t border-border-color">
        <h4 className="text-sm font-medium text-text-primary mb-3">
          API Usage This Month
        </h4>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="text-text-secondary text-sm">Requests</span>
            <span className="text-text-primary font-medium">3,250 / 10,000</span>
          </div>
          <div className="w-full bg-surface-light rounded-full h-2">
            <div 
              className="bg-gradient-primary h-2 rounded-full transition-all duration-500"
              style={{ width: '32.5%' }}
            ></div>
          </div>
          <div className="flex justify-between items-center text-xs text-text-muted">
            <span>Free tier limit</span>
            <span>Resets in 12 days</span>
          </div>
        </div>
      </div>

      {/* Quick Start */}
      <div className="mt-6 pt-6 border-t border-border-color">
        <h4 className="text-sm font-medium text-text-primary mb-3">
          Quick Start
        </h4>
        <div className="bg-surface-light rounded-lg p-3">
          <code className="text-xs text-text-secondary block">
            curl -H "Authorization: Bearer {apiKey}" \<br />
            &nbsp;&nbsp;https://api.zpass.dev/v1/wallet/analytics
          </code>
        </div>
      </div>
    </motion.div>
  )
}
