'use client'

import { motion } from 'framer-motion'
import { ExternalLink, Github, Zap, TrendingUp, Edit, Trash2 } from 'lucide-react'
import { formatNumber } from '@/lib/utils'

// Mock data - replace with real data from Supabase
const mockApplications = [
  {
    id: '1',
    name: 'DeFi Portfolio Tracker',
    description: 'A comprehensive portfolio tracking app that uses zPass wallet analytics to provide insights into DeFi positions and yield farming strategies.',
    appUrl: 'https://defi-tracker.app',
    githubUrl: 'https://github.com/user/defi-tracker',
    points: 5420,
    apiCalls: 2100,
    rank: 8,
    isActive: true,
    createdAt: '2024-01-15',
  },
  {
    id: '2',
    name: 'Wallet Risk Analyzer',
    description: 'Security-focused app that analyzes wallet risk using zPass reputation scoring and transaction history APIs.',
    appUrl: 'https://wallet-risk.io',
    githubUrl: null,
    points: 3000,
    apiCalls: 1150,
    rank: 15,
    isActive: true,
    createdAt: '2024-01-20',
  },
]

export default function UserApplications() {
  const handleEdit = (id: string) => {
    // TODO: Implement edit functionality
    console.log('Edit application:', id)
  }

  const handleDelete = (id: string) => {
    // TODO: Implement delete functionality
    console.log('Delete application:', id)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
      className="card p-6"
    >
      <h3 className="text-lg font-semibold text-text-primary mb-6">
        Your Applications ({mockApplications.length})
      </h3>

      {mockApplications.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-surface-light rounded-full flex items-center justify-center mx-auto mb-4">
            <Zap className="w-8 h-8 text-text-muted" />
          </div>
          <h4 className="text-text-primary font-medium mb-2">No applications yet</h4>
          <p className="text-text-secondary text-sm">
            Submit your first application to start competing in the leaderboard.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {mockApplications.map((app, index) => (
            <motion.div
              key={app.id}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="border border-border-color rounded-lg p-6 hover:border-border-light transition-colors duration-200"
            >
              {/* Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <h4 className="text-lg font-semibold text-text-primary">
                      {app.name}
                    </h4>
                    <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                      app.isActive 
                        ? 'bg-success/10 text-success' 
                        : 'bg-warning/10 text-warning'
                    }`}>
                      {app.isActive ? 'Active' : 'Inactive'}
                    </div>
                  </div>
                  <p className="text-text-secondary text-sm mb-3">
                    {app.description}
                  </p>
                  <div className="flex items-center space-x-4 text-sm">
                    <a
                      href={app.appUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center space-x-1 text-primary hover:text-primary-light"
                    >
                      <ExternalLink className="w-4 h-4" />
                      <span>Live App</span>
                    </a>
                    {app.githubUrl && (
                      <a
                        href={app.githubUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-1 text-text-muted hover:text-text-primary"
                      >
                        <Github className="w-4 h-4" />
                        <span>GitHub</span>
                      </a>
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleEdit(app.id)}
                    className="p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200"
                    title="Edit application"
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => handleDelete(app.id)}
                    className="p-2 rounded-lg text-text-muted hover:text-danger hover:bg-danger/10 transition-colors duration-200"
                    title="Delete application"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Stats */}
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-accent/10">
                    <Zap className="w-4 h-4 text-accent" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-text-primary">
                      {formatNumber(app.points)}
                    </div>
                    <div className="text-text-muted text-xs">Points</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-primary/10">
                    <TrendingUp className="w-4 h-4 text-primary" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-text-primary">
                      {formatNumber(app.apiCalls)}
                    </div>
                    <div className="text-text-muted text-xs">API Calls</div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <div className="p-2 rounded-lg bg-warning/10">
                    <TrendingUp className="w-4 h-4 text-warning" />
                  </div>
                  <div>
                    <div className="text-lg font-bold text-text-primary">
                      #{app.rank}
                    </div>
                    <div className="text-text-muted text-xs">Rank</div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div className="mt-4 pt-4 border-t border-border-color flex items-center justify-between text-xs text-text-muted">
                <span>Created {new Date(app.createdAt).toLocaleDateString()}</span>
                <span>Last updated 2 hours ago</span>
              </div>
            </motion.div>
          ))}
        </div>
      )}
    </motion.div>
  )
}
