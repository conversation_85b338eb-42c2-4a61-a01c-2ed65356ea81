'use client'

import { motion } from 'framer-motion'
import { Zap, Trophy, Code, Users, TrendingUp, Calendar } from 'lucide-react'
import { formatNumber } from '@/lib/utils'

// Mock data - replace with real data from Supabase
const mockStats = {
  totalPoints: 8420,
  totalApiCalls: 3250,
  currentRank: 12,
  applicationsCount: 2,
  referralsCount: 3,
  pointsThisWeek: 1240,
  apiCallsThisWeek: 890,
}

const stats = [
  {
    title: 'Total Points',
    value: formatNumber(mockStats.totalPoints),
    icon: Zap,
    color: 'text-accent',
    bgColor: 'bg-accent/10',
    change: '+12%',
    changeType: 'positive' as const,
  },
  {
    title: 'Current Rank',
    value: `#${mockStats.currentRank}`,
    icon: Trophy,
    color: 'text-warning',
    bgColor: 'bg-warning/10',
    change: '+3',
    changeType: 'positive' as const,
  },
  {
    title: 'API Calls',
    value: formatNumber(mockStats.totalApiCalls),
    icon: Code,
    color: 'text-primary',
    bgColor: 'bg-primary/10',
    change: '+24%',
    changeType: 'positive' as const,
  },
  {
    title: 'Applications',
    value: mockStats.applicationsCount.toString(),
    icon: Users,
    color: 'text-secondary',
    bgColor: 'bg-secondary/10',
    change: '+1',
    changeType: 'positive' as const,
  },
]

const weeklyStats = [
  {
    title: 'Points This Week',
    value: formatNumber(mockStats.pointsThisWeek),
    icon: TrendingUp,
    color: 'text-accent',
  },
  {
    title: 'API Calls This Week',
    value: formatNumber(mockStats.apiCallsThisWeek),
    icon: Calendar,
    color: 'text-primary',
  },
  {
    title: 'Referrals',
    value: mockStats.referralsCount.toString(),
    icon: Users,
    color: 'text-secondary',
  },
]

export default function DashboardStats() {
  return (
    <div className="space-y-6">
      {/* Main Stats Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: index * 0.1 }}
            className="card p-6 hover:glow-purple"
          >
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
              <div className={`text-sm font-medium ${
                stat.changeType === 'positive' ? 'text-success' : 'text-danger'
              }`}>
                {stat.change}
              </div>
            </div>
            <div className="space-y-1">
              <div className="text-2xl font-bold text-text-primary">
                {stat.value}
              </div>
              <div className="text-text-muted text-sm">
                {stat.title}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Weekly Stats */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.4 }}
        className="card p-6"
      >
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          Weekly Performance
        </h3>
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-6">
          {weeklyStats.map((stat, index) => (
            <div key={stat.title} className="flex items-center space-x-3">
              <div className="p-2 rounded-lg bg-surface-light">
                <stat.icon className={`w-5 h-5 ${stat.color}`} />
              </div>
              <div>
                <div className="text-xl font-bold text-text-primary">
                  {stat.value}
                </div>
                <div className="text-text-muted text-sm">
                  {stat.title}
                </div>
              </div>
            </div>
          ))}
        </div>
      </motion.div>

      {/* Progress Bars */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.6 }}
        className="card p-6"
      >
        <h3 className="text-lg font-semibold text-text-primary mb-4">
          Progress to Next Milestone
        </h3>
        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span className="text-text-secondary">Points to Top 10</span>
              <span className="text-text-primary">1,580 / 2,000</span>
            </div>
            <div className="w-full bg-surface-light rounded-full h-2">
              <div 
                className="bg-gradient-primary h-2 rounded-full transition-all duration-500"
                style={{ width: '79%' }}
              ></div>
            </div>
          </div>
          <div>
            <div className="flex justify-between text-sm mb-2">
              <span className="text-text-secondary">API Calls Goal</span>
              <span className="text-text-primary">3,250 / 5,000</span>
            </div>
            <div className="w-full bg-surface-light rounded-full h-2">
              <div 
                className="bg-gradient-to-r from-secondary to-accent h-2 rounded-full transition-all duration-500"
                style={{ width: '65%' }}
              ></div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  )
}
