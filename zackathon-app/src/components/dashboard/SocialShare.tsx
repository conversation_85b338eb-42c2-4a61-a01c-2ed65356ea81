'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Twitter, Share2, Copy, ExternalLink } from 'lucide-react'
import { generateShareText } from '@/lib/utils'

export default function SocialShare() {
  const [copied, setCopied] = useState(false)
  
  // Mock data - replace with real user data
  const userData = {
    appName: 'DeFi Portfolio Tracker',
    rank: 8,
    points: 5420,
  }

  const shareText = generateShareText(userData.appName, userData.rank, userData.points)
  const shareUrl = 'https://zackathon.dev'

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareText)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const shareOnTwitter = () => {
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareText)}&url=${encodeURIComponent(shareUrl)}`
    window.open(twitterUrl, '_blank', 'noopener,noreferrer')
  }

  const shareNative = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'zackathon - Season 1',
          text: shareText,
          url: shareUrl,
        })
      } catch (err) {
        console.error('Error sharing:', err)
      }
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="card p-6"
    >
      <div className="flex items-center space-x-3 mb-4">
        <div className="p-2 rounded-lg bg-accent/10">
          <Share2 className="w-5 h-5 text-accent" />
        </div>
        <h3 className="text-lg font-semibold text-text-primary">
          Share Your Progress
        </h3>
      </div>

      <p className="text-text-secondary text-sm mb-4">
        Share your achievements on social media to inspire other builders and grow the community!
      </p>

      {/* Preview */}
      <div className="bg-surface-light border border-border-color rounded-lg p-4 mb-4">
        <div className="flex items-start space-x-3">
          <div className="w-10 h-10 bg-gradient-primary rounded-full flex items-center justify-center flex-shrink-0">
            <span className="text-white font-bold text-sm">z</span>
          </div>
          <div className="flex-1 min-w-0">
            <div className="text-text-primary text-sm whitespace-pre-line">
              {shareText}
            </div>
            <div className="mt-2 text-primary text-sm">
              {shareUrl}
            </div>
          </div>
        </div>
      </div>

      {/* Share Buttons */}
      <div className="space-y-3">
        <button
          onClick={shareOnTwitter}
          className="w-full flex items-center justify-center space-x-2 p-3 bg-[#1DA1F2] hover:bg-[#1a91da] text-white rounded-lg transition-colors duration-200"
        >
          <Twitter className="w-4 h-4" />
          <span>Share on Twitter</span>
          <ExternalLink className="w-4 h-4" />
        </button>

        <div className="flex space-x-2">
          <div className="relative flex-1">
            <button
              onClick={copyToClipboard}
              className="w-full btn-secondary flex items-center justify-center space-x-2"
            >
              <Copy className="w-4 h-4" />
              <span>{copied ? 'Copied!' : 'Copy Text'}</span>
            </button>
            {copied && (
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-success text-white text-xs px-2 py-1 rounded">
                Copied to clipboard!
              </div>
            )}
          </div>
          
          {navigator.share && (
            <button
              onClick={shareNative}
              className="btn-secondary flex items-center justify-center space-x-2 px-4"
            >
              <Share2 className="w-4 h-4" />
              <span>Share</span>
            </button>
          )}
        </div>
      </div>

      {/* Tips */}
      <div className="mt-6 pt-6 border-t border-border-color">
        <h4 className="text-sm font-medium text-text-primary mb-3">
          💡 Sharing Tips
        </h4>
        <ul className="space-y-2 text-xs text-text-secondary">
          <li className="flex items-start space-x-2">
            <span className="text-accent">•</span>
            <span>Tag @zpass_dev to get featured on our official channels</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-accent">•</span>
            <span>Use hashtags #zackathon #BuildOnzPass #Web3</span>
          </li>
          <li className="flex items-start space-x-2">
            <span className="text-accent">•</span>
            <span>Share screenshots of your app for better engagement</span>
          </li>
        </ul>
      </div>
    </motion.div>
  )
}
