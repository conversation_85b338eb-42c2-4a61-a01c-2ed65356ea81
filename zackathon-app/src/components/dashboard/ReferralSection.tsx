'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Users, Co<PERSON>, Gift, UserPlus } from 'lucide-react'

export default function ReferralSection() {
  const [copied, setCopied] = useState(false)
  
  // Mock data - replace with real user data
  const referralCode = 'ZACK2024-USER123'
  const referralUrl = `https://zackathon.dev?ref=${referralCode}`
  const referralStats = {
    totalReferrals: 3,
    bonusPoints: 300,
    pendingReferrals: 1,
  }

  const copyReferralLink = async () => {
    try {
      await navigator.clipboard.writeText(referralUrl)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.4 }}
      className="card p-6"
    >
      <div className="flex items-center space-x-3 mb-4">
        <div className="p-2 rounded-lg bg-secondary/10">
          <Users className="w-5 h-5 text-secondary" />
        </div>
        <h3 className="text-lg font-semibold text-text-primary">
          Refer Builders
        </h3>
      </div>

      <p className="text-text-secondary text-sm mb-6">
        Invite other builders to join zackathon and earn bonus points for each successful referral!
      </p>

      {/* Referral Stats */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-secondary mb-1">
            {referralStats.totalReferrals}
          </div>
          <div className="text-text-muted text-xs">
            Total Referrals
          </div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-accent mb-1">
            {referralStats.bonusPoints}
          </div>
          <div className="text-text-muted text-xs">
            Bonus Points
          </div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-warning mb-1">
            {referralStats.pendingReferrals}
          </div>
          <div className="text-text-muted text-xs">
            Pending
          </div>
        </div>
      </div>

      {/* Referral Link */}
      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-2">
            Your Referral Link
          </label>
          <div className="flex items-center space-x-2">
            <div className="flex-1 p-3 bg-surface-light border border-border-color rounded-lg">
              <code className="text-sm text-text-primary break-all">
                {referralUrl}
              </code>
            </div>
            <div className="relative">
              <button
                onClick={copyReferralLink}
                className="p-3 rounded-lg bg-primary hover:bg-primary-dark text-white transition-colors duration-200"
                title="Copy referral link"
              >
                <Copy className="w-4 h-4" />
              </button>
              {copied && (
                <div className="absolute -top-8 right-0 bg-success text-white text-xs px-2 py-1 rounded whitespace-nowrap">
                  Copied!
                </div>
              )}
            </div>
          </div>
        </div>

        {/* How it Works */}
        <div className="bg-surface-light rounded-lg p-4">
          <h4 className="text-sm font-medium text-text-primary mb-3 flex items-center space-x-2">
            <Gift className="w-4 h-4 text-accent" />
            <span>How Referrals Work</span>
          </h4>
          <ul className="space-y-2 text-xs text-text-secondary">
            <li className="flex items-start space-x-2">
              <span className="text-accent font-bold">1.</span>
              <span>Share your referral link with other builders</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="text-accent font-bold">2.</span>
              <span>They sign up and submit their first application</span>
            </li>
            <li className="flex items-start space-x-2">
              <span className="text-accent font-bold">3.</span>
              <span>You both get 100 bonus points!</span>
            </li>
          </ul>
        </div>

        {/* Referral History */}
        {referralStats.totalReferrals > 0 && (
          <div>
            <h4 className="text-sm font-medium text-text-primary mb-3">
              Recent Referrals
            </h4>
            <div className="space-y-2">
              <div className="flex items-center justify-between p-3 bg-surface-light rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                    <UserPlus className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-text-primary">
                      <EMAIL>
                    </div>
                    <div className="text-xs text-text-muted">
                      Joined 2 days ago
                    </div>
                  </div>
                </div>
                <div className="text-sm font-medium text-success">
                  +100 pts
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-surface-light rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                    <UserPlus className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-text-primary">
                      <EMAIL>
                    </div>
                    <div className="text-xs text-text-muted">
                      Joined 5 days ago
                    </div>
                  </div>
                </div>
                <div className="text-sm font-medium text-success">
                  +100 pts
                </div>
              </div>
              <div className="flex items-center justify-between p-3 bg-surface-light rounded-lg opacity-60">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-warning/20 rounded-full flex items-center justify-center">
                    <UserPlus className="w-4 h-4 text-warning" />
                  </div>
                  <div>
                    <div className="text-sm font-medium text-text-primary">
                      <EMAIL>
                    </div>
                    <div className="text-xs text-text-muted">
                      Signed up, pending first app
                    </div>
                  </div>
                </div>
                <div className="text-sm font-medium text-warning">
                  Pending
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </motion.div>
  )
}
