'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Plus, Globe, Github, FileText, Loader } from 'lucide-react'

export default function ApplicationForm() {
  const [isOpen, setIsOpen] = useState(false)
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    appUrl: '',
    githubUrl: '',
    apiKey: '',
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // TODO: Submit to Supabase
      console.log('Submitting application:', formData)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Reset form and close
      setFormData({
        name: '',
        description: '',
        appUrl: '',
        githubUrl: '',
        apiKey: '',
      })
      setIsOpen(false)
    } catch (error) {
      console.error('Error submitting application:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 0.2 }}
      className="card p-6"
    >
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-text-primary">
          Submit New Application
        </h3>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="btn-primary flex items-center space-x-2"
        >
          <Plus className="w-4 h-4" />
          <span>Add Application</span>
        </button>
      </div>

      {isOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          transition={{ duration: 0.3 }}
          className="border-t border-border-color pt-6"
        >
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Application Name */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Application Name *
              </label>
              <input
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                className="w-full px-4 py-3 bg-surface-light border border-border-color rounded-lg text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="My Awesome DeFi App"
                required
              />
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Description *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="w-full px-4 py-3 bg-surface-light border border-border-color rounded-lg text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
                placeholder="Describe what your application does and how it uses zPass APIs..."
                required
              />
            </div>

            {/* App URL */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                Live Application URL *
              </label>
              <div className="relative">
                <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-muted" />
                <input
                  type="url"
                  name="appUrl"
                  value={formData.appUrl}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-surface-light border border-border-color rounded-lg text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="https://myapp.com"
                  required
                />
              </div>
            </div>

            {/* GitHub URL */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                GitHub Repository (Optional)
              </label>
              <div className="relative">
                <Github className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-muted" />
                <input
                  type="url"
                  name="githubUrl"
                  value={formData.githubUrl}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-surface-light border border-border-color rounded-lg text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="https://github.com/username/repo"
                />
              </div>
              <p className="text-xs text-text-muted mt-1">
                Adding a GitHub repository gives you bonus points!
              </p>
            </div>

            {/* API Key */}
            <div>
              <label className="block text-sm font-medium text-text-secondary mb-2">
                zPass API Key *
              </label>
              <div className="relative">
                <FileText className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-text-muted" />
                <input
                  type="text"
                  name="apiKey"
                  value={formData.apiKey}
                  onChange={handleInputChange}
                  className="w-full pl-10 pr-4 py-3 bg-surface-light border border-border-color rounded-lg text-text-primary placeholder-text-muted focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="zp_live_..."
                  required
                />
              </div>
              <p className="text-xs text-text-muted mt-1">
                We need your API key to track usage and calculate points
              </p>
            </div>

            {/* Submit Buttons */}
            <div className="flex items-center space-x-3 pt-4">
              <button
                type="submit"
                disabled={loading}
                className="btn-primary flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <Loader className="w-4 h-4 animate-spin" />
                    <span>Submitting...</span>
                  </>
                ) : (
                  <>
                    <Plus className="w-4 h-4" />
                    <span>Submit Application</span>
                  </>
                )}
              </button>
              <button
                type="button"
                onClick={() => setIsOpen(false)}
                className="btn-secondary"
              >
                Cancel
              </button>
            </div>
          </form>
        </motion.div>
      )}

      {!isOpen && (
        <p className="text-text-secondary text-sm">
          Submit your application to participate in the leaderboard and compete for prizes. 
          Make sure your app is live and uses zPass APIs.
        </p>
      )}
    </motion.div>
  )
}
