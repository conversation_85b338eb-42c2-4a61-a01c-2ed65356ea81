'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { ChevronDown, ChevronUp } from 'lucide-react'

const faqs = [
  {
    id: 1,
    question: 'What is zackathon?',
    answer: 'zackathon is a hackathon for Web3 builders to create applications using zPass APIs. It\'s Season 1 focused on wallet-level intelligence and DeFi analytics.',
    category: 'general',
  },
  {
    id: 2,
    question: 'How do I participate?',
    answer: 'Sign up with your email or Google account, get your API key, build an application using zPass APIs, and submit your project through the dashboard.',
    category: 'participation',
  },
  {
    id: 3,
    question: 'How are points calculated?',
    answer: 'Points are based on API usage volume, types of APIs used, and user engagement metrics. Premium APIs like wallet-analytics have higher multipliers (2x), DeFi scores (1.8x), reputation (1.5x), and transaction history (1.2x).',
    category: 'scoring',
  },
  {
    id: 4,
    question: 'What APIs are available?',
    answer: 'zPass offers wallet analytics, DeFi scores, reputation systems, transaction history, and basic wallet intelligence APIs. Each API provides different insights into wallet behavior and Web3 activity.',
    category: 'apis',
  },
  {
    id: 5,
    question: 'How does the referral system work?',
    answer: 'Refer other builders to earn bonus points. Both you and the referred builder get 100 bonus points when they join and start building. Share your referral link from the dashboard.',
    category: 'referrals',
  },
  {
    id: 6,
    question: 'When does the hackathon end?',
    answer: 'Season 1 runs for 8 weeks from the launch date. Check the leaderboard for real-time rankings and stay updated on our social channels for announcements.',
    category: 'timeline',
  },
  {
    id: 7,
    question: 'What are the prizes?',
    answer: 'Total prize pool is $50K with multiple categories: Grand Prize ($15K), Best Innovation ($10K), Best UX ($8K), Community Choice ($5K), and multiple runner-up prizes.',
    category: 'prizes',
  },
  {
    id: 8,
    question: 'Can I work in a team?',
    answer: 'Yes! Teams of up to 4 members are allowed. All team members must register individually and one person submits the project with all team member details.',
    category: 'participation',
  },
]

export default function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([])

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    )
  }

  return (
    <section id="faq" className="py-20 bg-surface/20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-text-primary mb-4">
            Frequently Asked Questions
          </h2>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            Everything you need to know about zackathon Season 1
          </p>
        </motion.div>

        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <motion.div
              key={faq.id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="card overflow-hidden"
            >
              <button
                onClick={() => toggleItem(faq.id)}
                className="w-full p-6 text-left flex items-center justify-between hover:bg-surface-hover/50 transition-colors duration-200"
              >
                <h3 className="text-lg font-semibold text-text-primary pr-4">
                  {faq.question}
                </h3>
                <div className="flex-shrink-0">
                  {openItems.includes(faq.id) ? (
                    <ChevronUp className="w-5 h-5 text-accent" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-text-muted" />
                  )}
                </div>
              </button>
              
              <AnimatePresence>
                {openItems.includes(faq.id) && (
                  <motion.div
                    initial={{ height: 0, opacity: 0 }}
                    animate={{ height: 'auto', opacity: 1 }}
                    exit={{ height: 0, opacity: 0 }}
                    transition={{ duration: 0.3 }}
                    className="overflow-hidden"
                  >
                    <div className="px-6 pb-6 border-t border-border-color">
                      <p className="text-text-secondary leading-relaxed pt-4">
                        {faq.answer}
                      </p>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          viewport={{ once: true }}
          className="text-center mt-12"
        >
          <div className="card p-8">
            <h3 className="text-xl font-semibold text-text-primary mb-4">
              Still have questions?
            </h3>
            <p className="text-text-secondary mb-6">
              Join our Discord community or reach out to our team for support
            </p>
            <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
              <a
                href="https://discord.gg/zpass"
                target="_blank"
                rel="noopener noreferrer"
                className="btn-primary"
              >
                Join Discord
              </a>
              <a
                href="mailto:<EMAIL>"
                className="btn-secondary"
              >
                Contact Support
              </a>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
