'use client'

import { motion } from 'framer-motion'
import { Trophy, Medal, Award, ExternalLink, Zap } from 'lucide-react'
import { formatNumber, getRankSuffix } from '@/lib/utils'
import type { LeaderboardEntry } from '@/lib/types'

// Mock data - replace with real data from Supabase
const mockLeaderboard: LeaderboardEntry[] = [
  {
    rank: 1,
    user_name: '<PERSON>',
    app_name: 'DeFi Portfolio Tracker',
    app_url: 'https://defi-tracker.app',
    points: 15420,
    api_calls: 8500,
    user_id: '1',
    application_id: '1',
  },
  {
    rank: 2,
    user_name: '<PERSON>',
    app_name: 'Wallet Risk Analyzer',
    app_url: 'https://wallet-risk.io',
    points: 12850,
    api_calls: 6200,
    user_id: '2',
    application_id: '2',
  },
  {
    rank: 3,
    user_name: '<PERSON>',
    app_name: 'Smart Contract Auditor',
    app_url: 'https://sc-audit.dev',
    points: 11200,
    api_calls: 5800,
    user_id: '3',
    application_id: '3',
  },
  {
    rank: 4,
    user_name: '<PERSON>',
    app_name: 'Yield Farming Optimizer',
    app_url: 'https://yield-optimizer.com',
    points: 9750,
    api_calls: 4900,
    user_id: '4',
    application_id: '4',
  },
  {
    rank: 5,
    user_name: 'David Park',
    app_name: 'NFT Collection Analyzer',
    app_url: 'https://nft-analyzer.xyz',
    points: 8900,
    api_calls: 4200,
    user_id: '5',
    application_id: '5',
  },
]

function getRankIcon(rank: number) {
  switch (rank) {
    case 1:
      return <Trophy className="w-6 h-6 text-yellow-400" />
    case 2:
      return <Medal className="w-6 h-6 text-gray-400" />
    case 3:
      return <Award className="w-6 h-6 text-amber-600" />
    default:
      return <span className="text-text-muted font-bold">#{rank}</span>
  }
}

export default function Leaderboard() {
  return (
    <section id="leaderboard" className="py-20 bg-surface/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
          className="text-center mb-12"
        >
          <h2 className="text-3xl sm:text-4xl font-bold text-text-primary mb-4">
            Season 1 Leaderboard
          </h2>
          <p className="text-lg text-text-secondary max-w-2xl mx-auto">
            See who's leading the pack in building innovative Web3 applications with zPass APIs
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
          className="card overflow-hidden"
        >
          {/* Table Header */}
          <div className="grid grid-cols-12 gap-4 p-6 border-b border-border-color bg-surface-light/50">
            <div className="col-span-1 text-text-muted font-semibold text-sm">Rank</div>
            <div className="col-span-3 text-text-muted font-semibold text-sm">Builder</div>
            <div className="col-span-3 text-text-muted font-semibold text-sm">Application</div>
            <div className="col-span-2 text-text-muted font-semibold text-sm">Points</div>
            <div className="col-span-2 text-text-muted font-semibold text-sm">API Calls</div>
            <div className="col-span-1 text-text-muted font-semibold text-sm">Link</div>
          </div>

          {/* Table Body */}
          <div className="divide-y divide-border-color">
            {mockLeaderboard.map((entry, index) => (
              <motion.div
                key={entry.application_id}
                initial={{ opacity: 0, x: -20 }}
                whileInView={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="grid grid-cols-12 gap-4 p-6 hover:bg-surface-hover/50 transition-colors duration-200"
              >
                {/* Rank */}
                <div className="col-span-1 flex items-center">
                  {getRankIcon(entry.rank)}
                </div>

                {/* Builder Name */}
                <div className="col-span-3 flex items-center">
                  <div>
                    <div className="font-semibold text-text-primary">{entry.user_name}</div>
                    <div className="text-sm text-text-muted">
                      {entry.rank}{getRankSuffix(entry.rank)} place
                    </div>
                  </div>
                </div>

                {/* Application */}
                <div className="col-span-3 flex items-center">
                  <div>
                    <div className="font-medium text-text-primary">{entry.app_name}</div>
                    <div className="text-sm text-text-secondary truncate max-w-[200px]">
                      {entry.app_url.replace('https://', '')}
                    </div>
                  </div>
                </div>

                {/* Points */}
                <div className="col-span-2 flex items-center">
                  <div className="flex items-center space-x-2">
                    <Zap className="w-4 h-4 text-accent" />
                    <span className="font-bold text-text-primary text-lg">
                      {formatNumber(entry.points)}
                    </span>
                  </div>
                </div>

                {/* API Calls */}
                <div className="col-span-2 flex items-center">
                  <div className="text-text-secondary">
                    {formatNumber(entry.api_calls)} calls
                  </div>
                </div>

                {/* Link */}
                <div className="col-span-1 flex items-center">
                  <a
                    href={entry.app_url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </a>
                </div>
              </motion.div>
            ))}
          </div>

          {/* View More */}
          <div className="p-6 border-t border-border-color text-center">
            <button className="btn-secondary">
              View Full Leaderboard
            </button>
          </div>
        </motion.div>

        {/* Mobile Responsive Version */}
        <div className="lg:hidden mt-8 space-y-4">
          {mockLeaderboard.map((entry, index) => (
            <motion.div
              key={entry.application_id}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              className="card p-6"
            >
              <div className="flex items-center justify-between mb-4">
                <div className="flex items-center space-x-3">
                  {getRankIcon(entry.rank)}
                  <div>
                    <div className="font-semibold text-text-primary">{entry.user_name}</div>
                    <div className="text-sm text-text-muted">{entry.rank}{getRankSuffix(entry.rank)} place</div>
                  </div>
                </div>
                <a
                  href={entry.app_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200"
                >
                  <ExternalLink className="w-5 h-5" />
                </a>
              </div>
              
              <div className="space-y-2">
                <div>
                  <div className="font-medium text-text-primary">{entry.app_name}</div>
                  <div className="text-sm text-text-secondary">{entry.app_url.replace('https://', '')}</div>
                </div>
                
                <div className="flex items-center justify-between pt-2">
                  <div className="flex items-center space-x-2">
                    <Zap className="w-4 h-4 text-accent" />
                    <span className="font-bold text-text-primary">{formatNumber(entry.points)} pts</span>
                  </div>
                  <div className="text-text-secondary text-sm">
                    {formatNumber(entry.api_calls)} calls
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
