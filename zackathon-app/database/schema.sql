-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT NOT NULL,
  name TEXT,
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Applications table
CREATE TABLE public.applications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  app_url TEXT NOT NULL,
  github_url TEXT,
  api_key TEXT NOT NULL UNIQUE,
  api_calls_count INTEGER DEFAULT 0,
  api_types_used TEXT[] DEFAULT '{}',
  user_engagement_score INTEGER DEFAULT 0,
  points INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API Usage tracking table
CREATE TABLE public.api_usage (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  application_id UUID REFERENCES public.applications(id) ON DELETE CASCADE,
  api_key TEXT NOT NULL,
  endpoint TEXT NOT NULL,
  api_type TEXT NOT NULL,
  calls_count INTEGER DEFAULT 1,
  date DATE DEFAULT CURRENT_DATE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(application_id, endpoint, date)
);

-- Referrals table
CREATE TABLE public.referrals (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  referrer_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  referred_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  bonus_points INTEGER DEFAULT 100,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(referrer_id, referred_id)
);

-- Points history table
CREATE TABLE public.points_history (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  application_id UUID REFERENCES public.applications(id) ON DELETE CASCADE,
  points_earned INTEGER NOT NULL,
  reason TEXT NOT NULL,
  api_calls INTEGER DEFAULT 0,
  api_type TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- FAQ table
CREATE TABLE public.faqs (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  question TEXT NOT NULL,
  answer TEXT NOT NULL,
  category TEXT NOT NULL,
  order_index INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_applications_user_id ON public.applications(user_id);
CREATE INDEX idx_applications_points ON public.applications(points DESC);
CREATE INDEX idx_api_usage_application_id ON public.api_usage(application_id);
CREATE INDEX idx_api_usage_date ON public.api_usage(date);
CREATE INDEX idx_points_history_user_id ON public.points_history(user_id);
CREATE INDEX idx_referrals_referrer_id ON public.referrals(referrer_id);

-- Row Level Security (RLS) policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.api_usage ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.referrals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.points_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.faqs ENABLE ROW LEVEL SECURITY;

-- Users policies
CREATE POLICY "Users can view their own profile" ON public.users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
  FOR UPDATE USING (auth.uid() = id);

-- Applications policies
CREATE POLICY "Users can view all applications" ON public.applications
  FOR SELECT USING (true);

CREATE POLICY "Users can create their own applications" ON public.applications
  FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own applications" ON public.applications
  FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own applications" ON public.applications
  FOR DELETE USING (auth.uid() = user_id);

-- API Usage policies
CREATE POLICY "Users can view their own API usage" ON public.api_usage
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.applications 
      WHERE applications.id = api_usage.application_id 
      AND applications.user_id = auth.uid()
    )
  );

-- Referrals policies
CREATE POLICY "Users can view referrals they made or received" ON public.referrals
  FOR SELECT USING (auth.uid() = referrer_id OR auth.uid() = referred_id);

CREATE POLICY "Users can create referrals" ON public.referrals
  FOR INSERT WITH CHECK (auth.uid() = referrer_id);

-- Points history policies
CREATE POLICY "Users can view their own points history" ON public.points_history
  FOR SELECT USING (auth.uid() = user_id);

-- FAQ policies (public read)
CREATE POLICY "Anyone can view FAQs" ON public.faqs
  FOR SELECT USING (true);

-- Functions for updating timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updating timestamps
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_applications_updated_at BEFORE UPDATE ON public.applications
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to calculate and update application points
CREATE OR REPLACE FUNCTION calculate_application_points(app_id UUID)
RETURNS INTEGER AS $$
DECLARE
  total_points INTEGER := 0;
  api_calls INTEGER := 0;
  api_types TEXT[];
  engagement INTEGER := 0;
BEGIN
  -- Get API usage data
  SELECT 
    COALESCE(SUM(calls_count), 0),
    ARRAY_AGG(DISTINCT api_type)
  INTO api_calls, api_types
  FROM public.api_usage 
  WHERE application_id = app_id;
  
  -- Get engagement score (placeholder - would be calculated based on actual usage)
  SELECT COALESCE(user_engagement_score, 0) 
  INTO engagement
  FROM public.applications 
  WHERE id = app_id;
  
  -- Calculate points based on API calls and types
  total_points := api_calls * 10;
  
  -- Add multipliers based on API types used
  IF 'wallet-analytics' = ANY(api_types) THEN
    total_points := total_points * 2;
  ELSIF 'defi-score' = ANY(api_types) THEN
    total_points := FLOOR(total_points * 1.8);
  ELSIF 'reputation' = ANY(api_types) THEN
    total_points := FLOOR(total_points * 1.5);
  ELSIF 'transaction-history' = ANY(api_types) THEN
    total_points := FLOOR(total_points * 1.2);
  END IF;
  
  -- Add engagement bonus
  total_points := total_points + (engagement * 5);
  
  -- Update the application
  UPDATE public.applications 
  SET 
    points = total_points,
    api_calls_count = api_calls,
    api_types_used = COALESCE(api_types, '{}'),
    updated_at = NOW()
  WHERE id = app_id;
  
  RETURN total_points;
END;
$$ LANGUAGE plpgsql;

-- Insert sample FAQs
INSERT INTO public.faqs (question, answer, category, order_index) VALUES
('What is zackathon?', 'zackathon is a hackathon for Web3 builders to create applications using zPass APIs. It''s Season 1 focused on wallet-level intelligence and DeFi analytics.', 'general', 1),
('How do I participate?', 'Sign up with your email or Google account, get your API key, build an application using zPass APIs, and submit your project through the dashboard.', 'participation', 2),
('How are points calculated?', 'Points are based on API usage volume, types of APIs used, and user engagement metrics. Premium APIs like wallet-analytics have higher multipliers.', 'scoring', 3),
('What APIs are available?', 'zPass offers wallet analytics, DeFi scores, reputation systems, transaction history, and basic wallet intelligence APIs.', 'apis', 4),
('How does the referral system work?', 'Refer other builders to earn bonus points. Both you and the referred builder get points when they join and start building.', 'referrals', 5),
('When does the hackathon end?', 'Season 1 runs for 8 weeks. Check the leaderboard for real-time rankings and stay updated on our social channels.', 'timeline', 6);
