{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function truncateAddress(address: string, chars = 4): string {\n  return `${address.slice(0, chars + 2)}...${address.slice(-chars)}`\n}\n\nexport function calculatePoints(apiCalls: number, apiType: string, userEngagement: number): number {\n  const basePoints = apiCalls * 10\n  \n  // API type multipliers\n  const typeMultipliers: Record<string, number> = {\n    'wallet-analytics': 2.0,\n    'defi-score': 1.8,\n    'reputation': 1.5,\n    'transaction-history': 1.2,\n    'basic': 1.0,\n  }\n  \n  const typeMultiplier = typeMultipliers[apiType] || 1.0\n  const engagementBonus = userEngagement * 5\n  \n  return Math.floor(basePoints * typeMultiplier + engagementBonus)\n}\n\nexport function getRankSuffix(rank: number): string {\n  if (rank % 100 >= 11 && rank % 100 <= 13) {\n    return 'th'\n  }\n  switch (rank % 10) {\n    case 1: return 'st'\n    case 2: return 'nd'\n    case 3: return 'rd'\n    default: return 'th'\n  }\n}\n\nexport function validateApiKey(apiKey: string): boolean {\n  // Basic API key validation - should be 32+ characters alphanumeric\n  return /^[a-zA-Z0-9]{32,}$/.test(apiKey)\n}\n\nexport function generateShareText(appName: string, rank: number, points: number): string {\n  return `🚀 Just built ${appName} using @zPass APIs and ranked #${rank} in #zackathon Season 1! \n  \n💎 ${points} points earned building on wallet-level intelligence\n🔥 Join the hackathon: zackathon.dev\n  \n#Web3 #BuildOnzPass #Hackathon`\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,gBAAgB,OAAe,EAAE,QAAQ,CAAC;IACxD,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,QAAQ,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,QAAQ;AACpE;AAEO,SAAS,gBAAgB,QAAgB,EAAE,OAAe,EAAE,cAAsB;IACvF,MAAM,aAAa,WAAW;IAE9B,uBAAuB;IACvB,MAAM,kBAA0C;QAC9C,oBAAoB;QACpB,cAAc;QACd,cAAc;QACd,uBAAuB;QACvB,SAAS;IACX;IAEA,MAAM,iBAAiB,eAAe,CAAC,QAAQ,IAAI;IACnD,MAAM,kBAAkB,iBAAiB;IAEzC,OAAO,KAAK,KAAK,CAAC,aAAa,iBAAiB;AAClD;AAEO,SAAS,cAAc,IAAY;IACxC,IAAI,OAAO,OAAO,MAAM,OAAO,OAAO,IAAI;QACxC,OAAO;IACT;IACA,OAAQ,OAAO;QACb,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf;YAAS,OAAO;IAClB;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,mEAAmE;IACnE,OAAO,qBAAqB,IAAI,CAAC;AACnC;AAEO,SAAS,kBAAkB,OAAe,EAAE,IAAY,EAAE,MAAc;IAC7E,OAAO,CAAC,cAAc,EAAE,QAAQ,+BAA+B,EAAE,KAAK;;GAErE,EAAE,OAAO;;;8BAGkB,CAAC;AAC/B", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Menu, X, Trophy, Code, BookOpen, Users } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n\n  const navItems = [\n    { name: 'Leaderboard', href: '#leaderboard', icon: Trophy },\n    { name: 'How to Participate', href: '#participate', icon: Code },\n    { name: 'About zPass', href: '#about', icon: BookOpen },\n    { name: 'Dashboard', href: '/dashboard', icon: Users },\n  ]\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border-color\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">z</span>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-text-primary font-bold text-lg leading-none\">zackathon</span>\n              <span className=\"text-text-muted text-xs leading-none\">Season 1</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-2 text-text-secondary hover:text-text-primary transition-colors duration-200\"\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Button */}\n          <div className=\"hidden md:block\">\n            <Link\n              href=\"/dashboard\"\n              className=\"btn-primary\"\n            >\n              Start Building\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"md:hidden p-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n          >\n            {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <div\n        className={cn(\n          \"md:hidden transition-all duration-300 ease-in-out\",\n          isOpen\n            ? \"max-h-96 opacity-100\"\n            : \"max-h-0 opacity-0 overflow-hidden\"\n        )}\n      >\n        <div className=\"px-4 py-4 space-y-4 bg-surface border-t border-border-color\">\n          {navItems.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              onClick={() => setIsOpen(false)}\n              className=\"flex items-center space-x-3 text-text-secondary hover:text-text-primary transition-colors duration-200 py-2\"\n            >\n              <item.icon className=\"w-5 h-5\" />\n              <span>{item.name}</span>\n            </Link>\n          ))}\n          <div className=\"pt-4 border-t border-border-color\">\n            <Link\n              href=\"/dashboard\"\n              onClick={() => setIsOpen(false)}\n              className=\"btn-primary w-full text-center block\"\n            >\n              Start Building\n            </Link>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,WAAW;QACf;YAAE,MAAM;YAAe,MAAM;YAAgB,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC1D;YAAE,MAAM;YAAsB,MAAM;YAAgB,MAAM,qMAAA,CAAA,OAAI;QAAC;QAC/D;YAAE,MAAM;YAAe,MAAM;YAAU,MAAM,iNAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,uMAAA,CAAA,QAAK;QAAC;KACtD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAmD;;;;;;sDACnE,6LAAC;4CAAK,WAAU;sDAAuC;;;;;;;;;;;;;;;;;;sCAK3D,6LAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;sCAMH,6LAAC;4BACC,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;sCAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5D,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA,SACI,yBACA;0BAGN,cAAA,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,qBACb,6LAAC,+JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,UAAU;gCACzB,WAAU;;kDAEV,6LAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,6LAAC;kDAAM,KAAK,IAAI;;;;;;;+BANX,KAAK,IAAI;;;;;sCASlB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,SAAS,IAAM,UAAU;gCACzB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GA7FwB;KAAA", "debugId": null}}, {"offset": {"line": 353, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/HeroSection.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { <PERSON>R<PERSON>, <PERSON>ap, Trophy, Code2, Users } from 'lucide-react'\n\nexport default function HeroSection() {\n  const stats = [\n    { label: 'Active Builders', value: '150+', icon: Users },\n    { label: 'Total Projects', value: '45', icon: Code2 },\n    { label: 'Prize Pool', value: '$50K', icon: Trophy },\n    { label: 'API Calls', value: '1M+', icon: Zap },\n  ]\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 bg-gradient-body\"></div>\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            className=\"inline-flex items-center space-x-2 bg-surface/50 backdrop-blur-sm border border-border-color rounded-full px-6 py-3\"\n          >\n            <div className=\"w-2 h-2 bg-success rounded-full animate-pulse\"></div>\n            <span className=\"text-text-secondary text-sm font-medium\">Season 1 • Live Now</span>\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n            className=\"space-y-4\"\n          >\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-text-primary leading-tight\">\n              Build the Future of{' '}\n              <span className=\"bg-gradient-primary bg-clip-text text-transparent\">\n                Web3 Intelligence\n              </span>\n            </h1>\n            <p className=\"text-lg sm:text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed\">\n              Join zackathon Season 1 and create groundbreaking applications using zPass wallet-level intelligence APIs. \n              Compete with builders worldwide and win amazing prizes.\n            </p>\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.8 }}\n            className=\"flex flex-col sm:flex-row items-center justify-center gap-4\"\n          >\n            <Link\n              href=\"/dashboard\"\n              className=\"btn-primary flex items-center space-x-2 text-lg px-8 py-4\"\n            >\n              <span>Start Building</span>\n              <ArrowRight className=\"w-5 h-5\" />\n            </Link>\n            <Link\n              href=\"#leaderboard\"\n              className=\"btn-secondary flex items-center space-x-2 text-lg px-8 py-4\"\n            >\n              <Trophy className=\"w-5 h-5\" />\n              <span>View Leaderboard</span>\n            </Link>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8, duration: 0.8 }}\n            className=\"grid grid-cols-2 lg:grid-cols-4 gap-6 mt-16\"\n          >\n            {stats.map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: 0.8 + index * 0.1, duration: 0.6 }}\n                className=\"card p-6 text-center hover:glow-purple\"\n              >\n                <stat.icon className=\"w-8 h-8 text-accent mx-auto mb-3\" />\n                <div className=\"text-2xl font-bold text-text-primary mb-1\">{stat.value}</div>\n                <div className=\"text-text-muted text-sm\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 1.2, duration: 0.8 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <div className=\"flex flex-col items-center space-y-2 text-text-muted\">\n          <span className=\"text-sm\">Scroll to explore</span>\n          <div className=\"w-6 h-10 border-2 border-border-color rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-accent rounded-full mt-2 animate-bounce\"></div>\n          </div>\n        </div>\n      </motion.div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAmB,OAAO;YAAQ,MAAM,uMAAA,CAAA,QAAK;QAAC;QACvD;YAAE,OAAO;YAAkB,OAAO;YAAM,MAAM,6MAAA,CAAA,QAAK;QAAC;QACpD;YAAE,OAAO;YAAc,OAAO;YAAQ,MAAM,yMAAA,CAAA,SAAM;QAAC;QACnD;YAAE,OAAO;YAAa,OAAO;YAAO,MAAM,mMAAA,CAAA,MAAG;QAAC;KAC/C;IAED,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAK,WAAU;8CAA0C;;;;;;;;;;;;sCAI5D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC;oCAAG,WAAU;;wCAA6E;wCACrE;sDACpB,6LAAC;4CAAK,WAAU;sDAAoD;;;;;;;;;;;;8CAItE,6LAAC;oCAAE,WAAU;8CAA2E;;;;;;;;;;;;sCAO1F,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC;sDAAK;;;;;;sDACN,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;sCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO,MAAM,QAAQ;wCAAK,UAAU;oCAAI;oCACtD,WAAU;;sDAEV,6LAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAI,WAAU;sDAA6C,KAAK,KAAK;;;;;;sDACtE,6LAAC;4CAAI,WAAU;sDAA2B,KAAK,KAAK;;;;;;;mCAR/C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0BAgBzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;gBACxC,WAAU;0BAEV,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;KArHwB", "debugId": null}}, {"offset": {"line": 735, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/Leaderboard.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Trophy, Medal, Award, ExternalLink, Zap } from 'lucide-react'\nimport { formatNumber, getRankSuffix } from '@/lib/utils'\nimport type { LeaderboardEntry } from '@/lib/types'\n\n// Mock data - replace with real data from Supabase\nconst mockLeaderboard: LeaderboardEntry[] = [\n  {\n    rank: 1,\n    user_name: '<PERSON>',\n    app_name: 'DeFi Portfolio Tracker',\n    app_url: 'https://defi-tracker.app',\n    points: 15420,\n    api_calls: 8500,\n    user_id: '1',\n    application_id: '1',\n  },\n  {\n    rank: 2,\n    user_name: '<PERSON>',\n    app_name: 'Wallet Risk Analyzer',\n    app_url: 'https://wallet-risk.io',\n    points: 12850,\n    api_calls: 6200,\n    user_id: '2',\n    application_id: '2',\n  },\n  {\n    rank: 3,\n    user_name: '<PERSON>',\n    app_name: 'Smart Contract Auditor',\n    app_url: 'https://sc-audit.dev',\n    points: 11200,\n    api_calls: 5800,\n    user_id: '3',\n    application_id: '3',\n  },\n  {\n    rank: 4,\n    user_name: '<PERSON>',\n    app_name: 'Yield Farming Optimizer',\n    app_url: 'https://yield-optimizer.com',\n    points: 9750,\n    api_calls: 4900,\n    user_id: '4',\n    application_id: '4',\n  },\n  {\n    rank: 5,\n    user_name: 'David Park',\n    app_name: 'NFT Collection Analyzer',\n    app_url: 'https://nft-analyzer.xyz',\n    points: 8900,\n    api_calls: 4200,\n    user_id: '5',\n    application_id: '5',\n  },\n]\n\nfunction getRankIcon(rank: number) {\n  switch (rank) {\n    case 1:\n      return <Trophy className=\"w-6 h-6 text-yellow-400\" />\n    case 2:\n      return <Medal className=\"w-6 h-6 text-gray-400\" />\n    case 3:\n      return <Award className=\"w-6 h-6 text-amber-600\" />\n    default:\n      return <span className=\"text-text-muted font-bold\">#{rank}</span>\n  }\n}\n\nexport default function Leaderboard() {\n  return (\n    <section id=\"leaderboard\" className=\"py-20 bg-surface/30\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-text-primary mb-4\">\n            Season 1 Leaderboard\n          </h2>\n          <p className=\"text-lg text-text-secondary max-w-2xl mx-auto\">\n            See who's leading the pack in building innovative Web3 applications with zPass APIs\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"card overflow-hidden\"\n        >\n          {/* Table Header */}\n          <div className=\"grid grid-cols-12 gap-4 p-6 border-b border-border-color bg-surface-light/50\">\n            <div className=\"col-span-1 text-text-muted font-semibold text-sm\">Rank</div>\n            <div className=\"col-span-3 text-text-muted font-semibold text-sm\">Builder</div>\n            <div className=\"col-span-3 text-text-muted font-semibold text-sm\">Application</div>\n            <div className=\"col-span-2 text-text-muted font-semibold text-sm\">Points</div>\n            <div className=\"col-span-2 text-text-muted font-semibold text-sm\">API Calls</div>\n            <div className=\"col-span-1 text-text-muted font-semibold text-sm\">Link</div>\n          </div>\n\n          {/* Table Body */}\n          <div className=\"divide-y divide-border-color\">\n            {mockLeaderboard.map((entry, index) => (\n              <motion.div\n                key={entry.application_id}\n                initial={{ opacity: 0, x: -20 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"grid grid-cols-12 gap-4 p-6 hover:bg-surface-hover/50 transition-colors duration-200\"\n              >\n                {/* Rank */}\n                <div className=\"col-span-1 flex items-center\">\n                  {getRankIcon(entry.rank)}\n                </div>\n\n                {/* Builder Name */}\n                <div className=\"col-span-3 flex items-center\">\n                  <div>\n                    <div className=\"font-semibold text-text-primary\">{entry.user_name}</div>\n                    <div className=\"text-sm text-text-muted\">\n                      {entry.rank}{getRankSuffix(entry.rank)} place\n                    </div>\n                  </div>\n                </div>\n\n                {/* Application */}\n                <div className=\"col-span-3 flex items-center\">\n                  <div>\n                    <div className=\"font-medium text-text-primary\">{entry.app_name}</div>\n                    <div className=\"text-sm text-text-secondary truncate max-w-[200px]\">\n                      {entry.app_url.replace('https://', '')}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Points */}\n                <div className=\"col-span-2 flex items-center\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Zap className=\"w-4 h-4 text-accent\" />\n                    <span className=\"font-bold text-text-primary text-lg\">\n                      {formatNumber(entry.points)}\n                    </span>\n                  </div>\n                </div>\n\n                {/* API Calls */}\n                <div className=\"col-span-2 flex items-center\">\n                  <div className=\"text-text-secondary\">\n                    {formatNumber(entry.api_calls)} calls\n                  </div>\n                </div>\n\n                {/* Link */}\n                <div className=\"col-span-1 flex items-center\">\n                  <a\n                    href={entry.app_url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n                  >\n                    <ExternalLink className=\"w-4 h-4\" />\n                  </a>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* View More */}\n          <div className=\"p-6 border-t border-border-color text-center\">\n            <button className=\"btn-secondary\">\n              View Full Leaderboard\n            </button>\n          </div>\n        </motion.div>\n\n        {/* Mobile Responsive Version */}\n        <div className=\"lg:hidden mt-8 space-y-4\">\n          {mockLeaderboard.map((entry, index) => (\n            <motion.div\n              key={entry.application_id}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"card p-6\"\n            >\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3\">\n                  {getRankIcon(entry.rank)}\n                  <div>\n                    <div className=\"font-semibold text-text-primary\">{entry.user_name}</div>\n                    <div className=\"text-sm text-text-muted\">{entry.rank}{getRankSuffix(entry.rank)} place</div>\n                  </div>\n                </div>\n                <a\n                  href={entry.app_url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n                >\n                  <ExternalLink className=\"w-5 h-5\" />\n                </a>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <div>\n                  <div className=\"font-medium text-text-primary\">{entry.app_name}</div>\n                  <div className=\"text-sm text-text-secondary\">{entry.app_url.replace('https://', '')}</div>\n                </div>\n                \n                <div className=\"flex items-center justify-between pt-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Zap className=\"w-4 h-4 text-accent\" />\n                    <span className=\"font-bold text-text-primary\">{formatNumber(entry.points)} pts</span>\n                  </div>\n                  <div className=\"text-text-secondary text-sm\">\n                    {formatNumber(entry.api_calls)} calls\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAOA,mDAAmD;AACnD,MAAM,kBAAsC;IAC1C;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;IACA;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;IACA;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;IACA;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;IACA;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;CACD;AAED,SAAS,YAAY,IAAY;IAC/B,OAAQ;QACN,KAAK;YACH,qBAAO,6LAAC,yMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC3B,KAAK;YACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B,KAAK;YACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B;YACE,qBAAO,6LAAC;gBAAK,WAAU;;oBAA4B;oBAAE;;;;;;;IACzD;AACF;AAEe,SAAS;IACtB,qBACE,6LAAC;QAAQ,IAAG;QAAc,WAAU;kBAClC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAK/D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,6LAAC;oCAAI,WAAU;8CAAmD;;;;;;;;;;;;sCAIpE,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;sDACZ,YAAY,MAAM,IAAI;;;;;;sDAIzB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAmC,MAAM,SAAS;;;;;;kEACjE,6LAAC;wDAAI,WAAU;;4DACZ,MAAM,IAAI;4DAAE,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI;4DAAE;;;;;;;;;;;;;;;;;;sDAM7C,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;;kEACC,6LAAC;wDAAI,WAAU;kEAAiC,MAAM,QAAQ;;;;;;kEAC9D,6LAAC;wDAAI,WAAU;kEACZ,MAAM,OAAO,CAAC,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;sDAMzC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,mMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM;;;;;;;;;;;;;;;;;sDAMhC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDACZ,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS;oDAAE;;;;;;;;;;;;sDAKnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,MAAM,MAAM,OAAO;gDACnB,QAAO;gDACP,KAAI;gDACJ,WAAU;0DAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;mCAzDvB,MAAM,cAAc;;;;;;;;;;sCAiE/B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAO,WAAU;0CAAgB;;;;;;;;;;;;;;;;;8BAOtC,6LAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;gDACZ,YAAY,MAAM,IAAI;8DACvB,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAmC,MAAM,SAAS;;;;;;sEACjE,6LAAC;4DAAI,WAAU;;gEAA2B,MAAM,IAAI;gEAAE,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI;gEAAE;;;;;;;;;;;;;;;;;;;sDAGpF,6LAAC;4CACC,MAAM,MAAM,OAAO;4CACnB,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,yNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAiC,MAAM,QAAQ;;;;;;8DAC9D,6LAAC;oDAAI,WAAU;8DAA+B,MAAM,OAAO,CAAC,OAAO,CAAC,YAAY;;;;;;;;;;;;sDAGlF,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6LAAC;4DAAK,WAAU;;gEAA+B,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM;gEAAE;;;;;;;;;;;;;8DAE5E,6LAAC;oDAAI,WAAU;;wDACZ,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS;wDAAE;;;;;;;;;;;;;;;;;;;;2BArChC,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;AA+CvC;KAnKwB", "debugId": null}}, {"offset": {"line": 1363, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/HowToParticipate.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { UserPlus, Key, Code, Upload, Trophy, ArrowRight } from 'lucide-react'\nimport Link from 'next/link'\n\nconst steps = [\n  {\n    icon: UserPlus,\n    title: 'Sign Up',\n    description: 'Create your account using email or Google authentication',\n    details: 'Quick and secure registration to get started with zackathon',\n  },\n  {\n    icon: Key,\n    title: 'Get API Key',\n    description: 'Access your zPass API key from the dashboard',\n    details: 'Free tier includes 10,000 API calls to get you started',\n  },\n  {\n    icon: Code,\n    title: 'Build Your App',\n    description: 'Create innovative applications using zPass APIs',\n    details: 'Use wallet analytics, DeFi scores, and reputation data',\n  },\n  {\n    icon: Upload,\n    title: 'Submit Project',\n    description: 'Upload your application and provide the live URL',\n    details: 'Include GitHub repository for bonus points',\n  },\n  {\n    icon: Trophy,\n    title: 'Compete & Win',\n    description: 'Climb the leaderboard and win amazing prizes',\n    details: 'Points based on API usage, innovation, and user engagement',\n  },\n]\n\nexport default function HowToParticipate() {\n  return (\n    <section id=\"participate\" className=\"py-20\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-text-primary mb-4\">\n            How to Participate\n          </h2>\n          <p className=\"text-lg text-text-secondary max-w-2xl mx-auto\">\n            Join zackathon Season 1 in just 5 simple steps and start building the future of Web3\n          </p>\n        </motion.div>\n\n        <div className=\"relative\">\n          {/* Connection Lines */}\n          <div className=\"hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-primary via-accent to-secondary transform -translate-y-1/2 opacity-30\"></div>\n\n          {/* Steps */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-4\">\n            {steps.map((step, index) => (\n              <motion.div\n                key={step.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"relative\"\n              >\n                {/* Step Card */}\n                <div className=\"card p-6 text-center hover:glow-purple relative z-10\">\n                  {/* Step Number */}\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center text-white font-bold text-sm\">\n                    {index + 1}\n                  </div>\n\n                  {/* Icon */}\n                  <div className=\"mb-4 flex justify-center\">\n                    <div className=\"w-16 h-16 bg-surface-light rounded-full flex items-center justify-center\">\n                      <step.icon className=\"w-8 h-8 text-accent\" />\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-xl font-semibold text-text-primary mb-2\">\n                    {step.title}\n                  </h3>\n                  <p className=\"text-text-secondary mb-3\">\n                    {step.description}\n                  </p>\n                  <p className=\"text-sm text-text-muted\">\n                    {step.details}\n                  </p>\n                </div>\n\n                {/* Arrow for desktop */}\n                {index < steps.length - 1 && (\n                  <div className=\"hidden lg:block absolute top-1/2 -right-2 transform -translate-y-1/2 z-20\">\n                    <ArrowRight className=\"w-6 h-6 text-accent\" />\n                  </div>\n                )}\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"card p-8 bg-gradient-card\">\n            <h3 className=\"text-2xl font-bold text-text-primary mb-4\">\n              Ready to Start Building?\n            </h3>\n            <p className=\"text-text-secondary mb-6 max-w-2xl mx-auto\">\n              Join hundreds of developers already building innovative Web3 applications with zPass APIs. \n              Get started today and compete for amazing prizes!\n            </p>\n            <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"btn-primary flex items-center space-x-2\"\n              >\n                <span>Get Started Now</span>\n                <ArrowRight className=\"w-5 h-5\" />\n              </Link>\n              <Link\n                href=\"#about\"\n                className=\"btn-secondary\"\n              >\n                Learn About zPass\n              </Link>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Quick Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\"\n        >\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-primary mb-2\">$50K</div>\n            <div className=\"text-text-muted\">Total Prize Pool</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-secondary mb-2\">8 Weeks</div>\n            <div className=\"text-text-muted\">Competition Duration</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-accent mb-2\">10K+</div>\n            <div className=\"text-text-muted\">Free API Calls</div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,QAAQ;IACZ;QACE,MAAM,iNAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA;QACE,MAAM,mMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA;QACE,MAAM,qMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA;QACE,MAAM,yMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,SAAS;IACX;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAQ,IAAG;QAAc,WAAU;kBAClC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAK/D,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;;;;;sCAGf,6LAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAGV,6LAAC;4CAAI,WAAU;;8DAEb,6LAAC;oDAAI,WAAU;8DACZ,QAAQ;;;;;;8DAIX,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKzB,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,6LAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAEnB,6LAAC;oDAAE,WAAU;8DACV,KAAK,OAAO;;;;;;;;;;;;wCAKhB,QAAQ,MAAM,MAAM,GAAG,mBACtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;mCApCrB,KAAK,KAAK;;;;;;;;;;;;;;;;8BA6CvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,6LAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,6LAAC;0DAAK;;;;;;0DACN,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAuC;;;;;;8CACtD,6LAAC;oCAAI,WAAU;8CAAkB;;;;;;;;;;;;sCAEnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAyC;;;;;;8CACxD,6LAAC;oCAAI,WAAU;8CAAkB;;;;;;;;;;;;sCAEnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAsC;;;;;;8CACrD,6LAAC;oCAAI,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7C;KAjIwB", "debugId": null}}]}