{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatNumber(num: number): string {\n  if (num >= 1000000) {\n    return (num / 1000000).toFixed(1) + 'M'\n  }\n  if (num >= 1000) {\n    return (num / 1000).toFixed(1) + 'K'\n  }\n  return num.toString()\n}\n\nexport function formatDate(date: Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(date)\n}\n\nexport function truncateAddress(address: string, chars = 4): string {\n  return `${address.slice(0, chars + 2)}...${address.slice(-chars)}`\n}\n\nexport function calculatePoints(apiCalls: number, apiType: string, userEngagement: number): number {\n  const basePoints = apiCalls * 10\n  \n  // API type multipliers\n  const typeMultipliers: Record<string, number> = {\n    'wallet-analytics': 2.0,\n    'defi-score': 1.8,\n    'reputation': 1.5,\n    'transaction-history': 1.2,\n    'basic': 1.0,\n  }\n  \n  const typeMultiplier = typeMultipliers[apiType] || 1.0\n  const engagementBonus = userEngagement * 5\n  \n  return Math.floor(basePoints * typeMultiplier + engagementBonus)\n}\n\nexport function getRankSuffix(rank: number): string {\n  if (rank % 100 >= 11 && rank % 100 <= 13) {\n    return 'th'\n  }\n  switch (rank % 10) {\n    case 1: return 'st'\n    case 2: return 'nd'\n    case 3: return 'rd'\n    default: return 'th'\n  }\n}\n\nexport function validateApiKey(apiKey: string): boolean {\n  // Basic API key validation - should be 32+ characters alphanumeric\n  return /^[a-zA-Z0-9]{32,}$/.test(apiKey)\n}\n\nexport function generateShareText(appName: string, rank: number, points: number): string {\n  return `🚀 Just built ${appName} using @zPass APIs and ranked #${rank} in #zackathon Season 1! \n  \n💎 ${points} points earned building on wallet-level intelligence\n🔥 Join the hackathon: zackathon.dev\n  \n#Web3 #BuildOnzPass #Hackathon`\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,aAAa,GAAW;IACtC,IAAI,OAAO,SAAS;QAClB,OAAO,CAAC,MAAM,OAAO,EAAE,OAAO,CAAC,KAAK;IACtC;IACA,IAAI,OAAO,MAAM;QACf,OAAO,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,KAAK;IACnC;IACA,OAAO,IAAI,QAAQ;AACrB;AAEO,SAAS,WAAW,IAAU;IACnC,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,gBAAgB,OAAe,EAAE,QAAQ,CAAC;IACxD,OAAO,GAAG,QAAQ,KAAK,CAAC,GAAG,QAAQ,GAAG,GAAG,EAAE,QAAQ,KAAK,CAAC,CAAC,QAAQ;AACpE;AAEO,SAAS,gBAAgB,QAAgB,EAAE,OAAe,EAAE,cAAsB;IACvF,MAAM,aAAa,WAAW;IAE9B,uBAAuB;IACvB,MAAM,kBAA0C;QAC9C,oBAAoB;QACpB,cAAc;QACd,cAAc;QACd,uBAAuB;QACvB,SAAS;IACX;IAEA,MAAM,iBAAiB,eAAe,CAAC,QAAQ,IAAI;IACnD,MAAM,kBAAkB,iBAAiB;IAEzC,OAAO,KAAK,KAAK,CAAC,aAAa,iBAAiB;AAClD;AAEO,SAAS,cAAc,IAAY;IACxC,IAAI,OAAO,OAAO,MAAM,OAAO,OAAO,IAAI;QACxC,OAAO;IACT;IACA,OAAQ,OAAO;QACb,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf,KAAK;YAAG,OAAO;QACf;YAAS,OAAO;IAClB;AACF;AAEO,SAAS,eAAe,MAAc;IAC3C,mEAAmE;IACnE,OAAO,qBAAqB,IAAI,CAAC;AACnC;AAEO,SAAS,kBAAkB,OAAe,EAAE,IAAY,EAAE,MAAc;IAC7E,OAAO,CAAC,cAAc,EAAE,QAAQ,+BAA+B,EAAE,KAAK;;GAErE,EAAE,OAAO;;;8BAGkB,CAAC;AAC/B", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/Navigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { Menu, X, Trophy, Code, BookOpen, Users, LogIn, LogOut, User } from 'lucide-react'\nimport { cn } from '@/lib/utils'\nimport { useAuth } from '@/contexts/AuthContext'\nimport LoginModal from './LoginModal'\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [showLoginModal, setShowLoginModal] = useState(false)\n  const { user, signOut, loading } = useAuth()\n\n  const navItems = [\n    { name: 'Leaderboard', href: '#leaderboard', icon: Trophy },\n    { name: 'How to Participate', href: '#participate', icon: Code },\n    { name: 'About zPass', href: '#about', icon: BookOpen },\n    { name: 'Dashboard', href: '/dashboard', icon: Users },\n  ]\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border-color\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-lg\">z</span>\n            </div>\n            <div className=\"flex flex-col\">\n              <span className=\"text-text-primary font-bold text-lg leading-none\">zackathon</span>\n              <span className=\"text-text-muted text-xs leading-none\">Season 1</span>\n            </div>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:flex items-center space-x-8\">\n            {navItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className=\"flex items-center space-x-2 text-text-secondary hover:text-text-primary transition-colors duration-200\"\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </Link>\n            ))}\n          </div>\n\n          {/* CTA Button / User Menu */}\n          <div className=\"hidden md:block\">\n            {user ? (\n              <div className=\"flex items-center space-x-4\">\n                <Link\n                  href=\"/dashboard\"\n                  className=\"btn-secondary flex items-center space-x-2\"\n                >\n                  <User className=\"w-4 h-4\" />\n                  <span>Dashboard</span>\n                </Link>\n                <button\n                  onClick={() => signOut()}\n                  className=\"p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n                  title=\"Sign out\"\n                >\n                  <LogOut className=\"w-5 h-5\" />\n                </button>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-3\">\n                <button\n                  onClick={() => setShowLoginModal(true)}\n                  className=\"btn-secondary flex items-center space-x-2\"\n                >\n                  <LogIn className=\"w-4 h-4\" />\n                  <span>Sign In</span>\n                </button>\n                <Link\n                  href=\"/dashboard\"\n                  className=\"btn-primary\"\n                >\n                  Start Building\n                </Link>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile menu button */}\n          <button\n            onClick={() => setIsOpen(!isOpen)}\n            className=\"md:hidden p-2 rounded-lg text-text-secondary hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n          >\n            {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </button>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      <div\n        className={cn(\n          \"md:hidden transition-all duration-300 ease-in-out\",\n          isOpen\n            ? \"max-h-96 opacity-100\"\n            : \"max-h-0 opacity-0 overflow-hidden\"\n        )}\n      >\n        <div className=\"px-4 py-4 space-y-4 bg-surface border-t border-border-color\">\n          {navItems.map((item) => (\n            <Link\n              key={item.name}\n              href={item.href}\n              onClick={() => setIsOpen(false)}\n              className=\"flex items-center space-x-3 text-text-secondary hover:text-text-primary transition-colors duration-200 py-2\"\n            >\n              <item.icon className=\"w-5 h-5\" />\n              <span>{item.name}</span>\n            </Link>\n          ))}\n          <div className=\"pt-4 border-t border-border-color\">\n            <Link\n              href=\"/dashboard\"\n              onClick={() => setIsOpen(false)}\n              className=\"btn-primary w-full text-center block\"\n            >\n              Start Building\n            </Link>\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AANA;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzC,MAAM,WAAW;QACf;YAAE,MAAM;YAAe,MAAM;YAAgB,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC1D;YAAE,MAAM;YAAsB,MAAM;YAAgB,MAAM,kMAAA,CAAA,OAAI;QAAC;QAC/D;YAAE,MAAM;YAAe,MAAM;YAAU,MAAM,8MAAA,CAAA,WAAQ;QAAC;QACtD;YAAE,MAAM;YAAa,MAAM;YAAc,MAAM,oMAAA,CAAA,QAAK;QAAC;KACtD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAK,WAAU;sDAAmD;;;;;;sDACnE,8OAAC;4CAAK,WAAU;sDAAuC;;;;;;;;;;;;;;;;;;sCAK3D,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,IAAI;;;;;;;mCALX,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;sCACZ,qBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCACC,SAAS,IAAM;wCACf,WAAU;wCACV,OAAM;kDAEN,cAAA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;;;;;;qDAItB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,kBAAkB;wCACjC,WAAU;;0DAEV,8OAAC,wMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAQP,8OAAC;4BACC,SAAS,IAAM,UAAU,CAAC;4BAC1B,WAAU;sCAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAM5D,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA,SACI,yBACA;0BAGN,cAAA,8OAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,SAAS,IAAM,UAAU;gCACzB,WAAU;;kDAEV,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAM,KAAK,IAAI;;;;;;;+BANX,KAAK,IAAI;;;;;sCASlB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,SAAS,IAAM,UAAU;gCACzB,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 453, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/HeroSection.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport Link from 'next/link'\nimport { <PERSON>R<PERSON>, <PERSON>ap, Trophy, Code2, Users } from 'lucide-react'\n\nexport default function HeroSection() {\n  const stats = [\n    { label: 'Active Builders', value: '150+', icon: Users },\n    { label: 'Total Projects', value: '45', icon: Code2 },\n    { label: 'Prize Pool', value: '$50K', icon: Trophy },\n    { label: 'API Calls', value: '1M+', icon: Zap },\n  ]\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden\">\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 bg-gradient-body\"></div>\n      <div className=\"absolute inset-0\">\n        <div className=\"absolute top-1/4 left-1/4 w-96 h-96 bg-primary/10 rounded-full blur-3xl animate-pulse\"></div>\n        <div className=\"absolute bottom-1/4 right-1/4 w-96 h-96 bg-accent/10 rounded-full blur-3xl animate-pulse delay-1000\"></div>\n      </div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"space-y-8\"\n        >\n          {/* Badge */}\n          <motion.div\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 0.2, duration: 0.6 }}\n            className=\"inline-flex items-center space-x-2 bg-surface/50 backdrop-blur-sm border border-border-color rounded-full px-6 py-3\"\n          >\n            <div className=\"w-2 h-2 bg-success rounded-full animate-pulse\"></div>\n            <span className=\"text-text-secondary text-sm font-medium\">Season 1 • Live Now</span>\n          </motion.div>\n\n          {/* Main Heading */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4, duration: 0.8 }}\n            className=\"space-y-4\"\n          >\n            <h1 className=\"text-4xl sm:text-5xl lg:text-6xl font-bold text-text-primary leading-tight\">\n              Build the Future of{' '}\n              <span className=\"bg-gradient-primary bg-clip-text text-transparent\">\n                Web3 Intelligence\n              </span>\n            </h1>\n            <p className=\"text-lg sm:text-xl text-text-secondary max-w-3xl mx-auto leading-relaxed\">\n              Join zackathon Season 1 and create groundbreaking applications using zPass wallet-level intelligence APIs. \n              Compete with builders worldwide and win amazing prizes.\n            </p>\n          </motion.div>\n\n          {/* CTA Buttons */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6, duration: 0.8 }}\n            className=\"flex flex-col sm:flex-row items-center justify-center gap-4\"\n          >\n            <Link\n              href=\"/dashboard\"\n              className=\"btn-primary flex items-center space-x-2 text-lg px-8 py-4\"\n            >\n              <span>Start Building</span>\n              <ArrowRight className=\"w-5 h-5\" />\n            </Link>\n            <Link\n              href=\"#leaderboard\"\n              className=\"btn-secondary flex items-center space-x-2 text-lg px-8 py-4\"\n            >\n              <Trophy className=\"w-5 h-5\" />\n              <span>View Leaderboard</span>\n            </Link>\n          </motion.div>\n\n          {/* Stats */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8, duration: 0.8 }}\n            className=\"grid grid-cols-2 lg:grid-cols-4 gap-6 mt-16\"\n          >\n            {stats.map((stat, index) => (\n              <motion.div\n                key={stat.label}\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: 0.8 + index * 0.1, duration: 0.6 }}\n                className=\"card p-6 text-center hover:glow-purple\"\n              >\n                <stat.icon className=\"w-8 h-8 text-accent mx-auto mb-3\" />\n                <div className=\"text-2xl font-bold text-text-primary mb-1\">{stat.value}</div>\n                <div className=\"text-text-muted text-sm\">{stat.label}</div>\n              </motion.div>\n            ))}\n          </motion.div>\n        </motion.div>\n      </div>\n\n      {/* Scroll Indicator */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 1.2, duration: 0.8 }}\n        className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2\"\n      >\n        <div className=\"flex flex-col items-center space-y-2 text-text-muted\">\n          <span className=\"text-sm\">Scroll to explore</span>\n          <div className=\"w-6 h-10 border-2 border-border-color rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-accent rounded-full mt-2 animate-bounce\"></div>\n          </div>\n        </div>\n      </motion.div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAmB,OAAO;YAAQ,MAAM,oMAAA,CAAA,QAAK;QAAC;QACvD;YAAE,OAAO;YAAkB,OAAO;YAAM,MAAM,0MAAA,CAAA,QAAK;QAAC;QACpD;YAAE,OAAO;YAAc,OAAO;YAAQ,MAAM,sMAAA,CAAA,SAAM;QAAC;QACnD;YAAE,OAAO;YAAa,OAAO;YAAO,MAAM,gMAAA,CAAA,MAAG;QAAC;KAC/C;IAED,qBACE,8OAAC;QAAQ,WAAU;;0BAEjB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAI;4BAClC,SAAS;gCAAE,SAAS;gCAAG,OAAO;4BAAE;4BAChC,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAK,WAAU;8CAA0C;;;;;;;;;;;;sCAI5D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC;oCAAG,WAAU;;wCAA6E;wCACrE;sDACpB,8OAAC;4CAAK,WAAU;sDAAoD;;;;;;;;;;;;8CAItE,8OAAC;oCAAE,WAAU;8CAA2E;;;;;;;;;;;;sCAO1F,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;;8CAEV,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC;sDAAK;;;;;;sDACN,8OAAC,kNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;8CAExB,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,sMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;sDAAK;;;;;;;;;;;;;;;;;;sCAKV,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;gCAAK,UAAU;4BAAI;4BACxC,WAAU;sCAET,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO,MAAM,QAAQ;wCAAK,UAAU;oCAAI;oCACtD,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAI,WAAU;sDAA6C,KAAK,KAAK;;;;;;sDACtE,8OAAC;4CAAI,WAAU;sDAA2B,KAAK,KAAK;;;;;;;mCAR/C,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0BAgBzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;oBAAK,UAAU;gBAAI;gBACxC,WAAU;0BAEV,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAU;;;;;;sCAC1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 829, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/Leaderboard.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Trophy, Medal, Award, ExternalLink, Zap } from 'lucide-react'\nimport { formatNumber, getRankSuffix } from '@/lib/utils'\nimport type { LeaderboardEntry } from '@/lib/types'\n\n// Mock data - replace with real data from Supabase\nconst mockLeaderboard: LeaderboardEntry[] = [\n  {\n    rank: 1,\n    user_name: '<PERSON>',\n    app_name: 'DeFi Portfolio Tracker',\n    app_url: 'https://defi-tracker.app',\n    points: 15420,\n    api_calls: 8500,\n    user_id: '1',\n    application_id: '1',\n  },\n  {\n    rank: 2,\n    user_name: '<PERSON>',\n    app_name: 'Wallet Risk Analyzer',\n    app_url: 'https://wallet-risk.io',\n    points: 12850,\n    api_calls: 6200,\n    user_id: '2',\n    application_id: '2',\n  },\n  {\n    rank: 3,\n    user_name: '<PERSON>',\n    app_name: 'Smart Contract Auditor',\n    app_url: 'https://sc-audit.dev',\n    points: 11200,\n    api_calls: 5800,\n    user_id: '3',\n    application_id: '3',\n  },\n  {\n    rank: 4,\n    user_name: '<PERSON>',\n    app_name: 'Yield Farming Optimizer',\n    app_url: 'https://yield-optimizer.com',\n    points: 9750,\n    api_calls: 4900,\n    user_id: '4',\n    application_id: '4',\n  },\n  {\n    rank: 5,\n    user_name: 'David Park',\n    app_name: 'NFT Collection Analyzer',\n    app_url: 'https://nft-analyzer.xyz',\n    points: 8900,\n    api_calls: 4200,\n    user_id: '5',\n    application_id: '5',\n  },\n]\n\nfunction getRankIcon(rank: number) {\n  switch (rank) {\n    case 1:\n      return <Trophy className=\"w-6 h-6 text-yellow-400\" />\n    case 2:\n      return <Medal className=\"w-6 h-6 text-gray-400\" />\n    case 3:\n      return <Award className=\"w-6 h-6 text-amber-600\" />\n    default:\n      return <span className=\"text-text-muted font-bold\">#{rank}</span>\n  }\n}\n\nexport default function Leaderboard() {\n  return (\n    <section id=\"leaderboard\" className=\"py-20 bg-surface/30\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-text-primary mb-4\">\n            Season 1 Leaderboard\n          </h2>\n          <p className=\"text-lg text-text-secondary max-w-2xl mx-auto\">\n            See who's leading the pack in building innovative Web3 applications with zPass APIs\n          </p>\n        </motion.div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          viewport={{ once: true }}\n          className=\"card overflow-hidden\"\n        >\n          {/* Table Header */}\n          <div className=\"grid grid-cols-12 gap-4 p-6 border-b border-border-color bg-surface-light/50\">\n            <div className=\"col-span-1 text-text-muted font-semibold text-sm\">Rank</div>\n            <div className=\"col-span-3 text-text-muted font-semibold text-sm\">Builder</div>\n            <div className=\"col-span-3 text-text-muted font-semibold text-sm\">Application</div>\n            <div className=\"col-span-2 text-text-muted font-semibold text-sm\">Points</div>\n            <div className=\"col-span-2 text-text-muted font-semibold text-sm\">API Calls</div>\n            <div className=\"col-span-1 text-text-muted font-semibold text-sm\">Link</div>\n          </div>\n\n          {/* Table Body */}\n          <div className=\"divide-y divide-border-color\">\n            {mockLeaderboard.map((entry, index) => (\n              <motion.div\n                key={entry.application_id}\n                initial={{ opacity: 0, x: -20 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"grid grid-cols-12 gap-4 p-6 hover:bg-surface-hover/50 transition-colors duration-200\"\n              >\n                {/* Rank */}\n                <div className=\"col-span-1 flex items-center\">\n                  {getRankIcon(entry.rank)}\n                </div>\n\n                {/* Builder Name */}\n                <div className=\"col-span-3 flex items-center\">\n                  <div>\n                    <div className=\"font-semibold text-text-primary\">{entry.user_name}</div>\n                    <div className=\"text-sm text-text-muted\">\n                      {entry.rank}{getRankSuffix(entry.rank)} place\n                    </div>\n                  </div>\n                </div>\n\n                {/* Application */}\n                <div className=\"col-span-3 flex items-center\">\n                  <div>\n                    <div className=\"font-medium text-text-primary\">{entry.app_name}</div>\n                    <div className=\"text-sm text-text-secondary truncate max-w-[200px]\">\n                      {entry.app_url.replace('https://', '')}\n                    </div>\n                  </div>\n                </div>\n\n                {/* Points */}\n                <div className=\"col-span-2 flex items-center\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Zap className=\"w-4 h-4 text-accent\" />\n                    <span className=\"font-bold text-text-primary text-lg\">\n                      {formatNumber(entry.points)}\n                    </span>\n                  </div>\n                </div>\n\n                {/* API Calls */}\n                <div className=\"col-span-2 flex items-center\">\n                  <div className=\"text-text-secondary\">\n                    {formatNumber(entry.api_calls)} calls\n                  </div>\n                </div>\n\n                {/* Link */}\n                <div className=\"col-span-1 flex items-center\">\n                  <a\n                    href={entry.app_url}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n                  >\n                    <ExternalLink className=\"w-4 h-4\" />\n                  </a>\n                </div>\n              </motion.div>\n            ))}\n          </div>\n\n          {/* View More */}\n          <div className=\"p-6 border-t border-border-color text-center\">\n            <button className=\"btn-secondary\">\n              View Full Leaderboard\n            </button>\n          </div>\n        </motion.div>\n\n        {/* Mobile Responsive Version */}\n        <div className=\"lg:hidden mt-8 space-y-4\">\n          {mockLeaderboard.map((entry, index) => (\n            <motion.div\n              key={entry.application_id}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"card p-6\"\n            >\n              <div className=\"flex items-center justify-between mb-4\">\n                <div className=\"flex items-center space-x-3\">\n                  {getRankIcon(entry.rank)}\n                  <div>\n                    <div className=\"font-semibold text-text-primary\">{entry.user_name}</div>\n                    <div className=\"text-sm text-text-muted\">{entry.rank}{getRankSuffix(entry.rank)} place</div>\n                  </div>\n                </div>\n                <a\n                  href={entry.app_url}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n                >\n                  <ExternalLink className=\"w-5 h-5\" />\n                </a>\n              </div>\n              \n              <div className=\"space-y-2\">\n                <div>\n                  <div className=\"font-medium text-text-primary\">{entry.app_name}</div>\n                  <div className=\"text-sm text-text-secondary\">{entry.app_url.replace('https://', '')}</div>\n                </div>\n                \n                <div className=\"flex items-center justify-between pt-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Zap className=\"w-4 h-4 text-accent\" />\n                    <span className=\"font-bold text-text-primary\">{formatNumber(entry.points)} pts</span>\n                  </div>\n                  <div className=\"text-text-secondary text-sm\">\n                    {formatNumber(entry.api_calls)} calls\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          ))}\n        </div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAOA,mDAAmD;AACnD,MAAM,kBAAsC;IAC1C;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;IACA;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;IACA;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;IACA;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;IACA;QACE,MAAM;QACN,WAAW;QACX,UAAU;QACV,SAAS;QACT,QAAQ;QACR,WAAW;QACX,SAAS;QACT,gBAAgB;IAClB;CACD;AAED,SAAS,YAAY,IAAY;IAC/B,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,sMAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;QAC3B,KAAK;YACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B,KAAK;YACH,qBAAO,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;QAC1B;YACE,qBAAO,8OAAC;gBAAK,WAAU;;oBAA4B;oBAAE;;;;;;;IACzD;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAc,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAK/D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;8CAAmD;;;;;;8CAClE,8OAAC;oCAAI,WAAU;8CAAmD;;;;;;;;;;;;sCAIpE,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAGV,8OAAC;4CAAI,WAAU;sDACZ,YAAY,MAAM,IAAI;;;;;;sDAIzB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAmC,MAAM,SAAS;;;;;;kEACjE,8OAAC;wDAAI,WAAU;;4DACZ,MAAM,IAAI;4DAAE,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI;4DAAE;;;;;;;;;;;;;;;;;;sDAM7C,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;;kEACC,8OAAC;wDAAI,WAAU;kEAAiC,MAAM,QAAQ;;;;;;kEAC9D,8OAAC;wDAAI,WAAU;kEACZ,MAAM,OAAO,CAAC,OAAO,CAAC,YAAY;;;;;;;;;;;;;;;;;sDAMzC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;kEACf,8OAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM;;;;;;;;;;;;;;;;;sDAMhC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;;oDACZ,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS;oDAAE;;;;;;;;;;;;sDAKnC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,MAAM,MAAM,OAAO;gDACnB,QAAO;gDACP,KAAI;gDACJ,WAAU;0DAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;;;;;;;;;;;;mCAzDvB,MAAM,cAAc;;;;;;;;;;sCAiE/B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAO,WAAU;0CAAgB;;;;;;;;;;;;;;;;;8BAOtC,8OAAC;oBAAI,WAAU;8BACZ,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,YAAY,MAAM,IAAI;8DACvB,8OAAC;;sEACC,8OAAC;4DAAI,WAAU;sEAAmC,MAAM,SAAS;;;;;;sEACjE,8OAAC;4DAAI,WAAU;;gEAA2B,MAAM,IAAI;gEAAE,CAAA,GAAA,mHAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,IAAI;gEAAE;;;;;;;;;;;;;;;;;;;sDAGpF,8OAAC;4CACC,MAAM,MAAM,OAAO;4CACnB,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAI5B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAI,WAAU;8DAAiC,MAAM,QAAQ;;;;;;8DAC9D,8OAAC;oDAAI,WAAU;8DAA+B,MAAM,OAAO,CAAC,OAAO,CAAC,YAAY;;;;;;;;;;;;sDAGlF,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,gMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;;gEAA+B,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM;gEAAE;;;;;;;;;;;;;8DAE5E,8OAAC;oDAAI,WAAU;;wDACZ,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,SAAS;wDAAE;;;;;;;;;;;;;;;;;;;;2BArChC,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;AA+CvC", "debugId": null}}, {"offset": {"line": 1451, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/HowToParticipate.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { UserPlus, Key, Code, Upload, Trophy, ArrowRight } from 'lucide-react'\nimport Link from 'next/link'\n\nconst steps = [\n  {\n    icon: UserPlus,\n    title: 'Sign Up',\n    description: 'Create your account using email or Google authentication',\n    details: 'Quick and secure registration to get started with zackathon',\n  },\n  {\n    icon: Key,\n    title: 'Get API Key',\n    description: 'Access your zPass API key from the dashboard',\n    details: 'Free tier includes 10,000 API calls to get you started',\n  },\n  {\n    icon: Code,\n    title: 'Build Your App',\n    description: 'Create innovative applications using zPass APIs',\n    details: 'Use wallet analytics, DeFi scores, and reputation data',\n  },\n  {\n    icon: Upload,\n    title: 'Submit Project',\n    description: 'Upload your application and provide the live URL',\n    details: 'Include GitHub repository for bonus points',\n  },\n  {\n    icon: Trophy,\n    title: 'Compete & Win',\n    description: 'Climb the leaderboard and win amazing prizes',\n    details: 'Points based on API usage, innovation, and user engagement',\n  },\n]\n\nexport default function HowToParticipate() {\n  return (\n    <section id=\"participate\" className=\"py-20\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-text-primary mb-4\">\n            How to Participate\n          </h2>\n          <p className=\"text-lg text-text-secondary max-w-2xl mx-auto\">\n            Join zackathon Season 1 in just 5 simple steps and start building the future of Web3\n          </p>\n        </motion.div>\n\n        <div className=\"relative\">\n          {/* Connection Lines */}\n          <div className=\"hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-primary via-accent to-secondary transform -translate-y-1/2 opacity-30\"></div>\n\n          {/* Steps */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 lg:gap-4\">\n            {steps.map((step, index) => (\n              <motion.div\n                key={step.title}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"relative\"\n              >\n                {/* Step Card */}\n                <div className=\"card p-6 text-center hover:glow-purple relative z-10\">\n                  {/* Step Number */}\n                  <div className=\"absolute -top-4 left-1/2 transform -translate-x-1/2 w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center text-white font-bold text-sm\">\n                    {index + 1}\n                  </div>\n\n                  {/* Icon */}\n                  <div className=\"mb-4 flex justify-center\">\n                    <div className=\"w-16 h-16 bg-surface-light rounded-full flex items-center justify-center\">\n                      <step.icon className=\"w-8 h-8 text-accent\" />\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <h3 className=\"text-xl font-semibold text-text-primary mb-2\">\n                    {step.title}\n                  </h3>\n                  <p className=\"text-text-secondary mb-3\">\n                    {step.description}\n                  </p>\n                  <p className=\"text-sm text-text-muted\">\n                    {step.details}\n                  </p>\n                </div>\n\n                {/* Arrow for desktop */}\n                {index < steps.length - 1 && (\n                  <div className=\"hidden lg:block absolute top-1/2 -right-2 transform -translate-y-1/2 z-20\">\n                    <ArrowRight className=\"w-6 h-6 text-accent\" />\n                  </div>\n                )}\n              </motion.div>\n            ))}\n          </div>\n        </div>\n\n        {/* CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.6 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"card p-8 bg-gradient-card\">\n            <h3 className=\"text-2xl font-bold text-text-primary mb-4\">\n              Ready to Start Building?\n            </h3>\n            <p className=\"text-text-secondary mb-6 max-w-2xl mx-auto\">\n              Join hundreds of developers already building innovative Web3 applications with zPass APIs. \n              Get started today and compete for amazing prizes!\n            </p>\n            <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4\">\n              <Link\n                href=\"/dashboard\"\n                className=\"btn-primary flex items-center space-x-2\"\n              >\n                <span>Get Started Now</span>\n                <ArrowRight className=\"w-5 h-5\" />\n              </Link>\n              <Link\n                href=\"#about\"\n                className=\"btn-secondary\"\n              >\n                Learn About zPass\n              </Link>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* Quick Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.8 }}\n          viewport={{ once: true }}\n          className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mt-12\"\n        >\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-primary mb-2\">$50K</div>\n            <div className=\"text-text-muted\">Total Prize Pool</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-secondary mb-2\">8 Weeks</div>\n            <div className=\"text-text-muted\">Competition Duration</div>\n          </div>\n          <div className=\"text-center\">\n            <div className=\"text-3xl font-bold text-accent mb-2\">10K+</div>\n            <div className=\"text-text-muted\">Free API Calls</div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAMA,MAAM,QAAQ;IACZ;QACE,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,SAAS;IACX;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;QACb,SAAS;IACX;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAc,WAAU;kBAClC,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAK/D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;;;;;sCAGf,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAGV,8OAAC;4CAAI,WAAU;;8DAEb,8OAAC;oDAAI,WAAU;8DACZ,QAAQ;;;;;;8DAIX,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,KAAK,IAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;8DAKzB,8OAAC;oDAAG,WAAU;8DACX,KAAK,KAAK;;;;;;8DAEb,8OAAC;oDAAE,WAAU;8DACV,KAAK,WAAW;;;;;;8DAEnB,8OAAC;oDAAE,WAAU;8DACV,KAAK,OAAO;;;;;;;;;;;;wCAKhB,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;mCApCrB,KAAK,KAAK;;;;;;;;;;;;;;;;8BA6CvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAI1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;;0DAEV,8OAAC;0DAAK;;;;;;0DACN,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;kDAExB,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;8BAQP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAuC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;8CAAkB;;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAyC;;;;;;8CACxD,8OAAC;oCAAI,WAAU;8CAAkB;;;;;;;;;;;;sCAEnC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAsC;;;;;;8CACrD,8OAAC;oCAAI,WAAU;8CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM7C", "debugId": null}}, {"offset": {"line": 1875, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/AboutzPass.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { Brain, Shield, Zap, TrendingUp, Users, Code, ExternalLink } from 'lucide-react'\n\nconst features = [\n  {\n    icon: Brain,\n    title: 'Wallet Intelligence',\n    description: 'Advanced analytics and insights into wallet behavior, transaction patterns, and DeFi activity.',\n  },\n  {\n    icon: Shield,\n    title: 'Reputation Scoring',\n    description: 'Comprehensive reputation system based on on-chain activity, transaction history, and protocol interactions.',\n  },\n  {\n    icon: TrendingUp,\n    title: 'DeFi Analytics',\n    description: 'Deep insights into DeFi positions, yield farming strategies, and protocol usage patterns.',\n  },\n  {\n    icon: Zap,\n    title: 'Real-time Data',\n    description: 'Live data feeds with sub-second latency for the most up-to-date wallet intelligence.',\n  },\n]\n\nconst useCases = [\n  {\n    title: 'DeFi Protocols',\n    description: 'Risk assessment, user scoring, and personalized recommendations',\n    examples: ['Lending risk evaluation', 'Yield optimization', 'Protocol governance'],\n  },\n  {\n    title: 'NFT Marketplaces',\n    description: 'User verification, collection analytics, and trading insights',\n    examples: ['Collector profiles', 'Market trends', 'Authenticity verification'],\n  },\n  {\n    title: 'Web3 Social',\n    description: 'Identity verification, reputation systems, and social scoring',\n    examples: ['Social credit', 'Community building', 'Influence metrics'],\n  },\n  {\n    title: 'Portfolio Trackers',\n    description: 'Comprehensive portfolio analytics and performance tracking',\n    examples: ['Asset allocation', 'Performance metrics', 'Risk analysis'],\n  },\n]\n\nexport default function AboutzPass() {\n  return (\n    <section id=\"about\" className=\"py-20\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-text-primary mb-4\">\n            About zPass\n          </h2>\n          <p className=\"text-lg text-text-secondary max-w-3xl mx-auto\">\n            zPass is the leading wallet-level intelligence API that provides developers with comprehensive \n            insights into Web3 user behavior, DeFi activity, and on-chain reputation.\n          </p>\n        </motion.div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20\">\n          {features.map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"card p-6 text-center hover:glow-purple\"\n            >\n              <div className=\"w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mx-auto mb-4\">\n                <feature.icon className=\"w-8 h-8 text-white\" />\n              </div>\n              <h3 className=\"text-xl font-semibold text-text-primary mb-3\">\n                {feature.title}\n              </h3>\n              <p className=\"text-text-secondary\">\n                {feature.description}\n              </p>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Use Cases */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"mb-20\"\n        >\n          <div className=\"text-center mb-12\">\n            <h3 className=\"text-2xl sm:text-3xl font-bold text-text-primary mb-4\">\n              Use Cases & Applications\n            </h3>\n            <p className=\"text-lg text-text-secondary max-w-2xl mx-auto\">\n              Build innovative applications across various Web3 verticals using zPass APIs\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8\">\n            {useCases.map((useCase, index) => (\n              <motion.div\n                key={useCase.title}\n                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}\n                whileInView={{ opacity: 1, x: 0 }}\n                transition={{ duration: 0.6, delay: index * 0.1 }}\n                viewport={{ once: true }}\n                className=\"card p-6\"\n              >\n                <h4 className=\"text-xl font-semibold text-text-primary mb-3\">\n                  {useCase.title}\n                </h4>\n                <p className=\"text-text-secondary mb-4\">\n                  {useCase.description}\n                </p>\n                <div className=\"space-y-2\">\n                  {useCase.examples.map((example, idx) => (\n                    <div key={idx} className=\"flex items-center space-x-2\">\n                      <div className=\"w-1.5 h-1.5 bg-accent rounded-full\"></div>\n                      <span className=\"text-text-muted text-sm\">{example}</span>\n                    </div>\n                  ))}\n                </div>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* API Stats */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"card p-8 bg-gradient-card mb-12\"\n        >\n          <div className=\"text-center mb-8\">\n            <h3 className=\"text-2xl font-bold text-text-primary mb-4\">\n              Trusted by Developers Worldwide\n            </h3>\n            <p className=\"text-text-secondary\">\n              Join thousands of developers building the future of Web3 with zPass\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-2 md:grid-cols-4 gap-8\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-primary mb-2\">500M+</div>\n              <div className=\"text-text-muted\">API Calls Served</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-secondary mb-2\">10K+</div>\n              <div className=\"text-text-muted\">Developers</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-accent mb-2\">50+</div>\n              <div className=\"text-text-muted\">Protocols Integrated</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-warning mb-2\">99.9%</div>\n              <div className=\"text-text-muted\">Uptime</div>\n            </div>\n          </div>\n        </motion.div>\n\n        {/* CTA Section */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center\"\n        >\n          <div className=\"card p-8\">\n            <h3 className=\"text-2xl font-bold text-text-primary mb-4\">\n              Ready to Build with zPass?\n            </h3>\n            <p className=\"text-text-secondary mb-6 max-w-2xl mx-auto\">\n              Get started with our comprehensive APIs and join the zackathon to compete for amazing prizes\n            </p>\n            <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4\">\n              <a\n                href=\"https://docs.zpass.dev\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"btn-primary flex items-center space-x-2\"\n              >\n                <span>View Documentation</span>\n                <ExternalLink className=\"w-4 h-4\" />\n              </a>\n              <a\n                href=\"/dashboard\"\n                className=\"btn-secondary\"\n              >\n                Start Building\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKA,MAAM,WAAW;IACf;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,gMAAA,CAAA,MAAG;QACT,OAAO;QACP,aAAa;IACf;CACD;AAED,MAAM,WAAW;IACf;QACE,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAA2B;YAAsB;SAAsB;IACpF;IACA;QACE,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAsB;YAAiB;SAA4B;IAChF;IACA;QACE,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAiB;YAAsB;SAAoB;IACxE;IACA;QACE,OAAO;QACP,aAAa;QACb,UAAU;YAAC;YAAoB;YAAuB;SAAgB;IACxE;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC;QAAQ,IAAG;QAAQ,WAAU;kBAC5B,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAO/D,8OAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,QAAQ,IAAI;wCAAC,WAAU;;;;;;;;;;;8CAE1B,8OAAC;oCAAG,WAAU;8CACX,QAAQ,KAAK;;;;;;8CAEhB,8OAAC;oCAAE,WAAU;8CACV,QAAQ,WAAW;;;;;;;2BAdjB,QAAQ,KAAK;;;;;;;;;;8BAqBxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwD;;;;;;8CAGtE,8OAAC;oCAAE,WAAU;8CAAgD;;;;;;;;;;;;sCAK/D,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,SAAS;wCAAG,GAAG,QAAQ,MAAM,IAAI,CAAC,KAAK;oCAAG;oCACrD,aAAa;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAChC,YAAY;wCAAE,UAAU;wCAAK,OAAO,QAAQ;oCAAI;oCAChD,UAAU;wCAAE,MAAM;oCAAK;oCACvB,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;sDACX,QAAQ,KAAK;;;;;;sDAEhB,8OAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;sDACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC9B,8OAAC;oDAAc,WAAU;;sEACvB,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAK,WAAU;sEAA2B;;;;;;;mDAFnC;;;;;;;;;;;mCAfT,QAAQ,KAAK;;;;;;;;;;;;;;;;8BA2B1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA4C;;;;;;8CAG1D,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;;;;;;;sCAKrC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAkB;;;;;;;;;;;;8CAEnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAyC;;;;;;sDACxD,8OAAC;4CAAI,WAAU;sDAAkB;;;;;;;;;;;;8CAEnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDAAkB;;;;;;;;;;;;8CAEnC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAuC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;sDAAkB;;;;;;;;;;;;;;;;;;;;;;;;8BAMvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA4C;;;;;;0CAG1D,8OAAC;gCAAE,WAAU;0CAA6C;;;;;;0CAG1D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;;0DAEV,8OAAC;0DAAK;;;;;;0DACN,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;kDAE1B,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2447, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/FAQ.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { ChevronDown, ChevronUp } from 'lucide-react'\n\nconst faqs = [\n  {\n    id: 1,\n    question: 'What is zackathon?',\n    answer: 'zackathon is a hackathon for Web3 builders to create applications using zPass APIs. It\\'s Season 1 focused on wallet-level intelligence and DeFi analytics.',\n    category: 'general',\n  },\n  {\n    id: 2,\n    question: 'How do I participate?',\n    answer: 'Sign up with your email or Google account, get your API key, build an application using zPass APIs, and submit your project through the dashboard.',\n    category: 'participation',\n  },\n  {\n    id: 3,\n    question: 'How are points calculated?',\n    answer: 'Points are based on API usage volume, types of APIs used, and user engagement metrics. Premium APIs like wallet-analytics have higher multipliers (2x), DeFi scores (1.8x), reputation (1.5x), and transaction history (1.2x).',\n    category: 'scoring',\n  },\n  {\n    id: 4,\n    question: 'What APIs are available?',\n    answer: 'zPass offers wallet analytics, DeFi scores, reputation systems, transaction history, and basic wallet intelligence APIs. Each API provides different insights into wallet behavior and Web3 activity.',\n    category: 'apis',\n  },\n  {\n    id: 5,\n    question: 'How does the referral system work?',\n    answer: 'Refer other builders to earn bonus points. Both you and the referred builder get 100 bonus points when they join and start building. Share your referral link from the dashboard.',\n    category: 'referrals',\n  },\n  {\n    id: 6,\n    question: 'When does the hackathon end?',\n    answer: 'Season 1 runs for 8 weeks from the launch date. Check the leaderboard for real-time rankings and stay updated on our social channels for announcements.',\n    category: 'timeline',\n  },\n  {\n    id: 7,\n    question: 'What are the prizes?',\n    answer: 'Total prize pool is $50K with multiple categories: Grand Prize ($15K), Best Innovation ($10K), Best UX ($8K), Community Choice ($5K), and multiple runner-up prizes.',\n    category: 'prizes',\n  },\n  {\n    id: 8,\n    question: 'Can I work in a team?',\n    answer: 'Yes! Teams of up to 4 members are allowed. All team members must register individually and one person submits the project with all team member details.',\n    category: 'participation',\n  },\n]\n\nexport default function FAQ() {\n  const [openItems, setOpenItems] = useState<number[]>([])\n\n  const toggleItem = (id: number) => {\n    setOpenItems(prev => \n      prev.includes(id) \n        ? prev.filter(item => item !== id)\n        : [...prev, id]\n    )\n  }\n\n  return (\n    <section id=\"faq\" className=\"py-20 bg-surface/20\">\n      <div className=\"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          viewport={{ once: true }}\n          className=\"text-center mb-12\"\n        >\n          <h2 className=\"text-3xl sm:text-4xl font-bold text-text-primary mb-4\">\n            Frequently Asked Questions\n          </h2>\n          <p className=\"text-lg text-text-secondary max-w-2xl mx-auto\">\n            Everything you need to know about zackathon Season 1\n          </p>\n        </motion.div>\n\n        <div className=\"space-y-4\">\n          {faqs.map((faq, index) => (\n            <motion.div\n              key={faq.id}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n              viewport={{ once: true }}\n              className=\"card overflow-hidden\"\n            >\n              <button\n                onClick={() => toggleItem(faq.id)}\n                className=\"w-full p-6 text-left flex items-center justify-between hover:bg-surface-hover/50 transition-colors duration-200\"\n              >\n                <h3 className=\"text-lg font-semibold text-text-primary pr-4\">\n                  {faq.question}\n                </h3>\n                <div className=\"flex-shrink-0\">\n                  {openItems.includes(faq.id) ? (\n                    <ChevronUp className=\"w-5 h-5 text-accent\" />\n                  ) : (\n                    <ChevronDown className=\"w-5 h-5 text-text-muted\" />\n                  )}\n                </div>\n              </button>\n              \n              <AnimatePresence>\n                {openItems.includes(faq.id) && (\n                  <motion.div\n                    initial={{ height: 0, opacity: 0 }}\n                    animate={{ height: 'auto', opacity: 1 }}\n                    exit={{ height: 0, opacity: 0 }}\n                    transition={{ duration: 0.3 }}\n                    className=\"overflow-hidden\"\n                  >\n                    <div className=\"px-6 pb-6 border-t border-border-color\">\n                      <p className=\"text-text-secondary leading-relaxed pt-4\">\n                        {faq.answer}\n                      </p>\n                    </div>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </motion.div>\n          ))}\n        </div>\n\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          viewport={{ once: true }}\n          className=\"text-center mt-12\"\n        >\n          <div className=\"card p-8\">\n            <h3 className=\"text-xl font-semibold text-text-primary mb-4\">\n              Still have questions?\n            </h3>\n            <p className=\"text-text-secondary mb-6\">\n              Join our Discord community or reach out to our team for support\n            </p>\n            <div className=\"flex flex-col sm:flex-row items-center justify-center gap-4\">\n              <a\n                href=\"https://discord.gg/zpass\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"btn-primary\"\n              >\n                Join Discord\n              </a>\n              <a\n                href=\"mailto:<EMAIL>\"\n                className=\"btn-secondary\"\n              >\n                Contact Support\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAJA;;;;;AAMA,MAAM,OAAO;IACX;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;IACA;QACE,IAAI;QACJ,UAAU;QACV,QAAQ;QACR,UAAU;IACZ;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEvD,MAAM,aAAa,CAAC;QAClB,aAAa,CAAA,OACX,KAAK,QAAQ,CAAC,MACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,MAC7B;mBAAI;gBAAM;aAAG;IAErB;IAEA,qBACE,8OAAC;QAAQ,IAAG;QAAM,WAAU;kBAC1B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;;sCAEV,8OAAC;4BAAG,WAAU;sCAAwD;;;;;;sCAGtE,8OAAC;4BAAE,WAAU;sCAAgD;;;;;;;;;;;;8BAK/D,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,KAAK,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,aAAa;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAChC,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;4BAChD,UAAU;gCAAE,MAAM;4BAAK;4BACvB,WAAU;;8CAEV,8OAAC;oCACC,SAAS,IAAM,WAAW,IAAI,EAAE;oCAChC,WAAU;;sDAEV,8OAAC;4CAAG,WAAU;sDACX,IAAI,QAAQ;;;;;;sDAEf,8OAAC;4CAAI,WAAU;sDACZ,UAAU,QAAQ,CAAC,IAAI,EAAE,kBACxB,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;qEAErB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAK7B,8OAAC,yLAAA,CAAA,kBAAe;8CACb,UAAU,QAAQ,CAAC,IAAI,EAAE,mBACxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,QAAQ;4CAAG,SAAS;wCAAE;wCACjC,SAAS;4CAAE,QAAQ;4CAAQ,SAAS;wCAAE;wCACtC,MAAM;4CAAE,QAAQ;4CAAG,SAAS;wCAAE;wCAC9B,YAAY;4CAAE,UAAU;wCAAI;wCAC5B,WAAU;kDAEV,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DACV,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;2BAlChB,IAAI,EAAE;;;;;;;;;;8BA4CjB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,WAAU;8BAEV,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA+C;;;;;;0CAG7D,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;0CAGxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;kDAGD,8OAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}, {"offset": {"line": 2770, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Zeru/zackathon/zackathon-app/src/components/Footer.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { Twitter, Github, Discord, Mail, ExternalLink } from 'lucide-react'\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  const footerLinks = {\n    hackathon: [\n      { name: 'Leaderboard', href: '#leaderboard' },\n      { name: 'How to Participate', href: '#participate' },\n      { name: 'FAQ', href: '#faq' },\n      { name: 'Dashboard', href: '/dashboard' },\n    ],\n    zpass: [\n      { name: 'About zPass', href: '#about' },\n      { name: 'Documentation', href: 'https://docs.zpass.dev', external: true },\n      { name: 'API Reference', href: 'https://api.zpass.dev', external: true },\n      { name: 'Status Page', href: 'https://status.zpass.dev', external: true },\n    ],\n    community: [\n      { name: 'Discord', href: 'https://discord.gg/zpass', external: true },\n      { name: 'Twitter', href: 'https://twitter.com/zpass_dev', external: true },\n      { name: 'GitHub', href: 'https://github.com/zpass-dev', external: true },\n      { name: 'Blog', href: 'https://blog.zpass.dev', external: true },\n    ],\n    legal: [\n      { name: 'Privacy Policy', href: '/privacy' },\n      { name: 'Terms of Service', href: '/terms' },\n      { name: 'Code of Conduct', href: '/code-of-conduct' },\n      { name: 'Contact', href: 'mailto:<EMAIL>', external: true },\n    ],\n  }\n\n  const socialLinks = [\n    { name: 'Twitter', href: 'https://twitter.com/zpass_dev', icon: Twitter },\n    { name: 'Discord', href: 'https://discord.gg/zpass', icon: Discord },\n    { name: 'GitHub', href: 'https://github.com/zpass-dev', icon: Github },\n    { name: 'Email', href: 'mailto:<EMAIL>', icon: Mail },\n  ]\n\n  return (\n    <footer className=\"bg-surface border-t border-border-color\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {/* Main Footer Content */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 mb-8\">\n          {/* Brand Section */}\n          <div className=\"lg:col-span-1\">\n            <Link href=\"/\" className=\"flex items-center space-x-2 mb-4\">\n              <div className=\"w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">z</span>\n              </div>\n              <div className=\"flex flex-col\">\n                <span className=\"text-text-primary font-bold text-lg leading-none\">zackathon</span>\n                <span className=\"text-text-muted text-xs leading-none\">Season 1</span>\n              </div>\n            </Link>\n            <p className=\"text-text-secondary text-sm mb-4\">\n              Build the future of Web3 with zPass wallet intelligence APIs. Join Season 1 and compete for $50K in prizes.\n            </p>\n            <div className=\"flex space-x-4\">\n              {socialLinks.map((social) => (\n                <a\n                  key={social.name}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"p-2 rounded-lg text-text-muted hover:text-text-primary hover:bg-surface-hover transition-colors duration-200\"\n                  aria-label={social.name}\n                >\n                  <social.icon className=\"w-5 h-5\" />\n                </a>\n              ))}\n            </div>\n          </div>\n\n          {/* Hackathon Links */}\n          <div>\n            <h3 className=\"text-text-primary font-semibold mb-4\">Hackathon</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.hackathon.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* zPass Links */}\n          <div>\n            <h3 className=\"text-text-primary font-semibold mb-4\">zPass</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.zpass.map((link) => (\n                <li key={link.name}>\n                  {link.external ? (\n                    <a\n                      href={link.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm flex items-center space-x-1\"\n                    >\n                      <span>{link.name}</span>\n                      <ExternalLink className=\"w-3 h-3\" />\n                    </a>\n                  ) : (\n                    <Link\n                      href={link.href}\n                      className=\"text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  )}\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Community Links */}\n          <div>\n            <h3 className=\"text-text-primary font-semibold mb-4\">Community</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.community.map((link) => (\n                <li key={link.name}>\n                  <a\n                    href={link.href}\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm flex items-center space-x-1\"\n                  >\n                    <span>{link.name}</span>\n                    <ExternalLink className=\"w-3 h-3\" />\n                  </a>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Legal Links */}\n          <div>\n            <h3 className=\"text-text-primary font-semibold mb-4\">Legal</h3>\n            <ul className=\"space-y-3\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.name}>\n                  {link.external ? (\n                    <a\n                      href={link.href}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm\"\n                    >\n                      {link.name}\n                    </a>\n                  ) : (\n                    <Link\n                      href={link.href}\n                      className=\"text-text-secondary hover:text-text-primary transition-colors duration-200 text-sm\"\n                    >\n                      {link.name}\n                    </Link>\n                  )}\n                </li>\n              ))}\n            </ul>\n          </div>\n        </div>\n\n        {/* Bottom Section */}\n        <div className=\"border-t border-border-color pt-8\">\n          <div className=\"flex flex-col md:flex-row items-center justify-between\">\n            <div className=\"text-text-muted text-sm mb-4 md:mb-0\">\n              © {currentYear} zPass. All rights reserved. Built for Web3 builders.\n            </div>\n            <div className=\"flex items-center space-x-6 text-sm\">\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-2 h-2 bg-success rounded-full animate-pulse\"></div>\n                <span className=\"text-text-muted\">Season 1 Live</span>\n              </div>\n              <div className=\"text-text-muted\">\n                Made with ❤️ for the Web3 community\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,MAAM,cAAc;QAClB,WAAW;YACT;gBAAE,MAAM;gBAAe,MAAM;YAAe;YAC5C;gBAAE,MAAM;gBAAsB,MAAM;YAAe;YACnD;gBAAE,MAAM;gBAAO,MAAM;YAAO;YAC5B;gBAAE,MAAM;gBAAa,MAAM;YAAa;SACzC;QACD,OAAO;YACL;gBAAE,MAAM;gBAAe,MAAM;YAAS;YACtC;gBAAE,MAAM;gBAAiB,MAAM;gBAA0B,UAAU;YAAK;YACxE;gBAAE,MAAM;gBAAiB,MAAM;gBAAyB,UAAU;YAAK;YACvE;gBAAE,MAAM;gBAAe,MAAM;gBAA4B,UAAU;YAAK;SACzE;QACD,WAAW;YACT;gBAAE,MAAM;gBAAW,MAAM;gBAA4B,UAAU;YAAK;YACpE;gBAAE,MAAM;gBAAW,MAAM;gBAAiC,UAAU;YAAK;YACzE;gBAAE,MAAM;gBAAU,MAAM;gBAAgC,UAAU;YAAK;YACvE;gBAAE,MAAM;gBAAQ,MAAM;gBAA0B,UAAU;YAAK;SAChE;QACD,OAAO;YACL;gBAAE,MAAM;gBAAkB,MAAM;YAAW;YAC3C;gBAAE,MAAM;gBAAoB,MAAM;YAAS;YAC3C;gBAAE,MAAM;gBAAmB,MAAM;YAAmB;YACpD;gBAAE,MAAM;gBAAW,MAAM;gBAA4B,UAAU;YAAK;SACrE;IACH;IAEA,MAAM,cAAc;QAClB;YAAE,MAAM;YAAW,MAAM;YAAiC,MAAM,wMAAA,CAAA,UAAO;QAAC;QACxE;YAAE,MAAM;YAAW,MAAM;YAA4B,MAAM,kLAAA,CAAA,UAAO;QAAC;QACnE;YAAE,MAAM;YAAU,MAAM;YAAgC,MAAM,sMAAA,CAAA,SAAM;QAAC;QACrE;YAAE,MAAM;YAAS,MAAM;YAA4B,MAAM,kMAAA,CAAA,OAAI;QAAC;KAC/D;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAmD;;;;;;8DACnE,8OAAC;oDAAK,WAAU;8DAAuC;;;;;;;;;;;;;;;;;;8CAG3D,8OAAC;oCAAE,WAAU;8CAAmC;;;;;;8CAGhD,8OAAC;oCAAI,WAAU;8CACZ,YAAY,GAAG,CAAC,CAAC,uBAChB,8OAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAY,OAAO,IAAI;sDAEvB,cAAA,8OAAC,OAAO,IAAI;gDAAC,WAAU;;;;;;2CAPlB,OAAO,IAAI;;;;;;;;;;;;;;;;sCAcxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACE,KAAK,QAAQ,iBACZ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,8OAAC;kEAAM,KAAK,IAAI;;;;;;kEAChB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;qEAG1B,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CAhBP,KAAK,IAAI;;;;;;;;;;;;;;;;sCAyBxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAG,WAAU;8CACX,YAAY,SAAS,CAAC,GAAG,CAAC,CAAC,qBAC1B,8OAAC;sDACC,cAAA,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,QAAO;gDACP,KAAI;gDACJ,WAAU;;kEAEV,8OAAC;kEAAM,KAAK,IAAI;;;;;;kEAChB,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;2CARnB,KAAK,IAAI;;;;;;;;;;;;;;;;sCAgBxB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,8OAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,8OAAC;sDACE,KAAK,QAAQ,iBACZ,8OAAC;gDACC,MAAM,KAAK,IAAI;gDACf,QAAO;gDACP,KAAI;gDACJ,WAAU;0DAET,KAAK,IAAI;;;;;qEAGZ,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CAfP,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAyB1B,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;oCAAuC;oCACjD;oCAAY;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAK,WAAU;0DAAkB;;;;;;;;;;;;kDAEpC,8OAAC;wCAAI,WAAU;kDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C", "debugId": null}}]}